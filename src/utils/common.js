import _ from 'lodash'

import { roles } from '@/accessConfig'
import { cities } from '@/cities/cities'
import { HOURS_IN_DAY, Kind, Role, SECONDS_IN_HOUR } from '@/const'
import { lineattr, payattr, taxmode, vat } from '@/pages/CreateOrderScreen/createOrderData'
import { unixToMoment } from '@/utils/date'

export const getRightNames = (num, one, two, five) => {
  let n = Math.abs(num)

  n %= 100

  if (n >= 5 && n <= 20) {
    return five
  }

  n %= 10

  if (n === 1) {
    return one
  }
  if (n >= 2 && n <= 4) {
    return two
  }

  return five
}

export const getDate = (data) => {
  const months = [
    'Января',
    'Февраля',
    'Марта',
    'Апреля',
    'Мая',
    'Июня',
    'Июля',
    'Августа',
    'Сентября',
    'Октября',
    'Но<PERSON><PERSON>ря',
    'Декабря',
  ]
  const days = ['Воскресенье', 'Понедельник', 'Вторник', 'Среда', 'Четверг', 'Пятница', 'Суббота']

  const time = {}
  const unixTime = new Date(unixToMoment(data))

  time.year = unixTime.getFullYear()
  time.month = months[unixTime.getMonth()]
  time.numMonth = unixTime.getMonth()
  time.date = unixTime.getDate()
  time.day = days[unixTime.getDay()]
  time.hour = unixTime.getHours()
  time.minute = unixTime.getMinutes()

  return time
}

export const times = {
  getTime(date = 1) {
    const obj = getDate(date)
    return ('0' + obj.hour).slice(-2) + ':' + ('0' + obj.minute).slice(-2)
  },

  getDateForDatepicker(date = 1) {
    const obj = getDate(date)
    const hour = ('0' + obj.hour).slice(-2)
    const minute = ('0' + obj.minute).slice(-2)
    const day = obj.date < 10 ? `0${obj.date}` : obj.date
    const month = obj.numMonth < 9 ? `0${obj.numMonth + 1}` : obj.numMonth + 1
    const year = obj.year.toString()
    const str = `${year}-${month}-${day}T${hour}:${minute}`
    return str
  },

  getDay(date = 1) {
    const obj = getDate(date)
    return obj.day
  },

  getDayOfWeek(date = 1) {
    const obj = getDate(date)
    return `${obj.date} ${obj.month}`
  },

  getFullDate(date = 1) {
    const obj = getDate(date)
    return `${obj.date} ${obj.month} ${obj.year}`
  },

  getShortDate(date = 1) {
    const obj = getDate(date)
    return `${obj.date} ${obj.month}`
  },

  getYear(date = 1) {
    const obj = getDate(date)
    return `${obj.year}`
  },

  getNumberDate(date = 1) {
    const obj = getDate(date)
    const day = obj.date < 10 ? `0${obj.date}` : obj.date
    const month = obj.numMonth < 9 ? `0${obj.numMonth + 1}` : obj.numMonth + 1
    // let year = obj.year.toString().slice(2, 4);
    const year = obj.year.toString()

    return `${year}-${month}-${day}`
  },
}

export const getDateToday = () => {
  const d = new Date()

  return (
    d.getFullYear() +
    '-' +
    ('0' + (d.getMonth() + 1)).slice(-2) +
    '-' +
    ('0' + d.getDate()).slice(-2) +
    'T' +
    ('0' + d.getHours()).slice(-2) +
    ':' +
    ('0' + d.getMinutes()).slice(-2)
  )
}

export const getSex = (sex) => {
  let sexLowerCase = ''
  if (sex !== null && sex !== undefined) {
    sexLowerCase = sex.toLowerCase()
  }

  switch (sexLowerCase) {
    case 'male':
      return 'Мужской'
    case 'female':
      return 'Женский'
    default:
      return ''
  }
}

export const getRole = (role) => {
  let roleLowerCase = ''
  if (role !== null && role !== undefined) roleLowerCase = role

  switch (roleLowerCase) {
    case 'user':
      return 'Пользователь'
    case 'admin':
      return 'Администратор'
    case 'volunteer':
      return 'Волонтёр'
    case 'promo_admin':
      return 'Администратор+'
    case 'superadmin':
      return 'Боженька'
    default:
      return ''
  }
}

export const recompose = (obj, string) => {
  const parts = string.split('.')
  const newObj = obj?.[parts[0]]
  if (parts[1]) {
    parts.splice(0, 1)
    const newString = parts.join('.')
    return recompose(newObj, newString)
  }
  return newObj
}

export const setOptions = (arr, value, label) => {
  if (arr.length > 0) {
    return arr.map((el) => {
      return { value: el[value], label: el[label] }
    })
  }
  return []
}

export const getOptionsCity = (id, name) => {
  return { value: id, label: name }
}

export const getOptionsSelect = (values, value, label) => {
  if (values?.length > 0) {
    return values?.map((item) => {
      return { value: recompose(item, value), label: recompose(item, label) }
    })
  } else {
    return []
  }
}

export const getDefaultOptionsSelect = (values) => {
  if (values?.length > 0) {
    return values?.map((item) => {
      return { value: item, label: item }
    })
  } else {
    return []
  }
}

export const getCityNameOptions = (id) => {
  const city = cities.find((city) => city.value === id)

  return getOptionsCity(city.value, city.label)
}

export const isEqual = (value, other) => {
  // Get the value type
  var type = Object.prototype.toString.call(value)

  // If the two objects are not the same type, return false
  if (type !== Object.prototype.toString.call(other)) return false

  // If items are not an object or array, return false
  if (['[object Array]', '[object Object]'].indexOf(type) < 0) return false

  // Compare the length of the length of the two items
  var valueLen = type === '[object Array]' ? value.length : Object.keys(value).length
  var otherLen = type === '[object Array]' ? other.length : Object.keys(other).length
  if (valueLen !== otherLen) return false

  // Compare two items
  var compare = function (item1, item2) {
    // Get the object type
    var itemType = Object.prototype.toString.call(item1)

    // If an object or array, compare recursively
    if (['[object Array]', '[object Object]'].indexOf(itemType) >= 0) {
      if (!isEqual(item1, item2)) return false
    }

    // Otherwise, do a simple comparison
    else {
      // If the two items are not the same type, return false
      if (itemType !== Object.prototype.toString.call(item2)) return false

      // Else if it's a function, convert to a string and compare
      // Otherwise, just compare
      if (itemType === '[object Function]') {
        if (item1.toString() !== item2.toString()) return false
      } else {
        if (item1 !== item2) return false
      }
    }
  }

  // Compare properties
  if (type === '[object Array]') {
    for (let i = 0; i < valueLen; i++) {
      if (compare(value[i], other[i]) === false) return false
    }
  } else {
    for (const key in value) {
      if (Object.prototype.hasOwnProperty.call(value, key)) {
        if (compare(value[key], other[key]) === false) return false
      }
    }
  }

  // If nothing failed, return true
  return true
}

export const getCityNameFromId = (eventCities, id) => {
  const cityObj = eventCities.find((city) => city.public_id === id)

  if (cityObj) {
    const cityName = cities.find((city) => city.value === cityObj.city.id)
    return cityName.label
  }

  return id
}

export const getTicketStatus = (status) => {
  switch (status) {
    case 'paid':
      return 'Оплачен'
    case 'canceled':
      return 'Возврат админ'
    case 'partially_canceled':
      return 'Возврат юзер'
    case 'created':
      return 'Не оплачен'
    case 'deleted':
      return 'Удалён'
    case 'authorized':
      return 'Авторизация'
    default:
      return status
  }
}

export const getStatusCode = (code) => {
  switch (code) {
    case 200:
      return 'Успешно'
    case 201:
      return 'Создано'
    case 400:
      return 'Неверный запрос'
    case 401:
      return 'Не авторизован'
    case 403:
      return 'Доступ запрещён'
    case 404:
      return 'Не найдено'
    case 422:
      return 'Ошибка валидации'
    case 500:
      return 'Ошибка сервера'
    default:
      return code?.toString() || '-'
  }
}

export const getKindName = (kind) => {
  switch (kind) {
    case Kind.ATHLETE:
      return 'Физик'
    case Kind.CORPORATE:
      return 'Корпоративный'
    case Kind.PARTNER:
      return 'Партнёр'
    case Kind.BRAND:
      return 'Бренд'
    case Kind.SPORT:
      return 'Спорт'
    case Kind.OTHER:
      return 'Другие'
    default:
      return kind
  }
}

export const getDiscountForm = (value, discount) => {
  switch (discount) {
    case 'percent':
      return `${value}%`
    case 'rub':
      return `${value}₽`
    default:
      return value
  }
}

export const toLowerCaseEmail = (email) => {
  return email.toLowerCase().replaceAll(' ', '')
}

export const getDescCode = (item, code) => {
  switch (item) {
    case 'taxmode':
      return taxmode.find((el) => el.code === code).desc
    case 'vat':
      return vat.find((el) => el.code === code).desc
    case 'payattr':
      return code > 0 ? payattr.find((el) => el.code === code).desc : 'Не известное значение'
    case 'lineattr':
      return code > 0 ? lineattr.find((el) => el.code === code).desc : 'Не известное значение'
    default:
      return ''
  }
}

export const getCityIdEventCityId = (string) => {
  if (string === '') {
    return ['', '']
  } else {
    return [+string.split('##')[0], string.split('##')[1]]
  }
}

export const convertBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const fileReader = new FileReader()
    fileReader.readAsDataURL(file)
    fileReader.onload = () => {
      resolve(fileReader.result)
    }
    fileReader.onerror = (error) => {
      reject(error)
    }
  })
}

export const checkUploadedImage = (file, width, height) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = (e) => {
      const image = new Image()
      image.src = e.target.result
      image.onload = (e) => {
        const imgWidth = e.target.width
        const imgHeight = e.target.height
        if ((imgWidth === width && imgHeight === height) || (width === 'all' && width === 'all')) {
          resolve(true)
        }
        resolve(false)
      }
    }
    reader.onerror = (e) => {
      reject(e)
    }
  })
}

export const sliceNewsSubtitle = (subtitle) => {
  if (subtitle?.length > 80) {
    return subtitle.slice(0, 80) + '...'
  } else {
    return subtitle
  }
}

const isObjAndEmpty = (value) => {
  if (typeof value === 'object') {
    return Object.keys(value).length === 0
  }
  return false
}

const isObjAndEmptyBranches = (value) => {
  if (typeof value === 'object') {
    return value === null ? false : Object.keys(value).length === 0
  }
  return false
}

export const removeEmptyString = (obj) => {
  return Object.fromEntries(
    Object.entries(obj)
      .filter(([, value]) => value !== '' && value !== undefined && !isObjAndEmpty(value))
      .map(([key, value]) => [key, value === Object(value) ? removeEmptyString(value) : value])
  )
}

export const removeEmptyString2 = (obj) => {
  const isObjAndEmpty = (value) =>
    value && typeof value === 'object' && !Array.isArray(value) && Object.keys(value).length === 0

  const isArrayAndEmpty = (value) => Array.isArray(value) && value.length === 0

  const shouldRemoveValue = (value) =>
    value === '' || value === undefined || isObjAndEmpty(value) || isArrayAndEmpty(value)

  const cleanObject = (obj) => {
    if (Array.isArray(obj)) {
      return obj
        .map((item) => (item === Object(item) ? cleanObject(item) : item))
        .filter((item) => !shouldRemoveValue(item))
    }

    return Object.entries(obj)
      .map(([key, value]) => [key, value === Object(value) ? cleanObject(value) : value])
      .filter(([, value]) => !shouldRemoveValue(value))
      .reduce((acc, [key, value]) => {
        if (!shouldRemoveValue(value)) {
          acc[key] = value
        }
        return acc
      }, {})
  }

  return cleanObject(obj)
}

export const cleanUnchangedArrays = (formData, defaultFormData, keysToCheck) => {
  const cleanedData = { ...formData }

  keysToCheck.forEach((key) => {
    // Проверка, содержит ли ключ точки, что указывает на вложенность
    if (key.includes('.')) {
      const parts = key.split('.')
      const parentKey = parts[0]
      const childKey = parts[1]

      // Получение значения из вложенных объектов
      const value = formData[parentKey]?.[childKey]
      const defaultValue = defaultFormData[parentKey]?.[childKey]

      if (Array.isArray(value) && Array.isArray(defaultValue)) {
        if (_.isEqual(value, defaultValue)) {
          // Если родительский объект существует, удалить только дочерний ключ
          if (cleanedData[parentKey]) {
            delete cleanedData[parentKey][childKey]

            // Если после удаления дочернего ключа родительский объект пуст, удалить его тоже
            if (Object.keys(cleanedData[parentKey]).length === 0) {
              delete cleanedData[parentKey]
            }
          }
        }
      } else if (Array.isArray(value) && value.length === 0 && defaultValue === undefined) {
        // Если родительский объект существует, удалить только дочерний ключ
        if (cleanedData[parentKey]) {
          delete cleanedData[parentKey][childKey]

          // Если после удаления дочернего ключа родительский объект пуст, удалить его тоже
          if (Object.keys(cleanedData[parentKey]).length === 0) {
            delete cleanedData[parentKey]
          }
        }
      }
    } else {
      // Обработка простых ключей как раньше
      const value = formData[key]
      const defaultValue = defaultFormData[key]

      if (Array.isArray(value) && Array.isArray(defaultValue)) {
        if (_.isEqual(value, defaultValue)) {
          delete cleanedData[key]
        }
      } else if (Array.isArray(value) && value.length === 0 && defaultValue === undefined) {
        delete cleanedData[key]
      }
    }
  })

  return cleanedData
}

export const removeEmptyStringBranches = (obj) => {
  return Object.fromEntries(
    Object.entries(obj)
      .filter(([, value]) => value !== '' && value !== undefined && !isObjAndEmptyBranches(value))
      .map(([key, value]) => [key, value === Object(value) ? removeEmptyString(value) : value])
  )
}

export const setValueAsText = (v, defaultV) => {
  if (v === defaultV || (v === '' && defaultV === undefined)) {
    return undefined
  } else if (v === '') {
    return null
  }

  return v
}

export const setValueAsNumber = (v, defaultV) => {
  if (v === defaultV || (v === '' && defaultV === undefined)) {
    return undefined
  } else if (v === '') {
    return null
  }

  return Number(v)
}

export const formatPhoneNumber = (number) => {
  if (number?.length === 11) {
    return number.replace(/\D+/g, '').replace(/(\d)(\d{3})(\d{3})(\d{2})(\d{2})/, '+$1 $2 $3-$4-$5')
  }

  return number
}

export const secondsToDays = (seconds) => {
  return seconds / (SECONDS_IN_HOUR * HOURS_IN_DAY)
}

export const toHHMMSS = (secs) => {
  const secNum = parseInt(secs.toString(), 10)
  const hours = Math.floor(secNum / 3600)
  const minutes = Math.floor(secNum / 60) % 60
  const seconds = secNum % 60

  const result = [hours, minutes, seconds]
    .map((val) => (val < 10 ? `0${val}` : val))
    .filter((val, index) => val !== '00' || index > 0)
    .join(':')

  if (hours === 0) return `00:${result}`

  return result
}

export const isAccessPermissions = (allowedRoles, userRoles) => {
  return Role.SUPERADMIN in userRoles || allowedRoles.some((el) => userRoles.includes(el))
}

export const getRoleName = (role) => {
  if (role in roles) {
    return roles[role]
  }

  return role
}

export const generateRegionOptions = (items) => {
  return items.map((item) => {
    return { value: item.id, label: item.name_ru, timezone: item.timezone }
  })
}
