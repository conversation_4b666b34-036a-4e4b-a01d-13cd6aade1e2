import { nanoid } from '@reduxjs/toolkit'

import { accessConfig } from '@/accessConfig'

import IconAthletes from './components/ui/icons/IconAthletes'
import IconBanners from './components/ui/icons/IconBanners'
import IconBranches from './components/ui/icons/IconBranches'
import IconCalendar from './components/ui/icons/IconCalendar'
import IconClub from './components/ui/icons/IconClub'
import IconCorp from './components/ui/icons/IconCorp'
import IconCustomOrder from './components/ui/icons/IconCustomOrder'
import IconDocuments from './components/ui/icons/IconDocuments'
import IconEvents from './components/ui/icons/IconEvents'
import IconInsurance from './components/ui/icons/IconInsurance'
import IconLinks from './components/ui/icons/IconLinks'
import IconNews from './components/ui/icons/IconNews'
import IconOnlineResults from './components/ui/icons/IconOnlineResults'
import IconOrders from './components/ui/icons/IconOrders'
import IconPages from './components/ui/icons/IconPages'
import IconPayment from './components/ui/icons/IconPayment'
import IconPolls from './components/ui/icons/IconPolls'
import IconProcessing from './components/ui/icons/IconProcessing'
import IconPromocode from './components/ui/icons/IconPromocode'
import IconResults from './components/ui/icons/IconResults'
import IconSettings from './components/ui/icons/IconSettings'
import IconShop from './components/ui/icons/IconShop'
import IconUsers from './components/ui/icons/IconUsers'
import { AppRoute } from './const'

const menuItems = [
  {
    id: nanoid(),
    path: AppRoute.PROMOCODES,
    label: 'Промокоды',
    allowedRoles: accessConfig.promocodes.route,
    icon: IconPromocode,
  },
  {
    id: nanoid(),
    path: AppRoute.EVENTS,
    label: 'События',
    allowedRoles: accessConfig.events.route,
    icon: IconEvents,
  },
  {
    id: nanoid(),
    path: AppRoute.ORDERS,
    label: 'Заказы',
    allowedRoles: accessConfig.orders.route,
    icon: IconOrders,
  },
  {
    id: nanoid(),
    path: AppRoute.NEWS,
    label: 'Новости',
    allowedRoles: accessConfig.news.route,
    icon: IconNews,
  },
  {
    id: nanoid(),
    path: AppRoute.USERS,
    label: 'Пользователи',
    allowedRoles: accessConfig.users.route,
    icon: IconUsers,
  },
  {
    id: nanoid(),
    path: AppRoute.CUSTOM_ORDER,
    label: 'Произвольный заказ',
    allowedRoles: accessConfig.customOrder.route,
    icon: IconCustomOrder,
  },
  {
    id: nanoid(),
    path: AppRoute.CORP,
    label: 'Корпы и партнёры',
    allowedRoles: accessConfig.customOrder.route,
    icon: IconCorp,
  },
  {
    id: nanoid(),
    path: AppRoute.SUPPLIER,
    label: 'Платежный Агент',
    allowedRoles: accessConfig.supplier.route,
    icon: IconPayment,
  },
  {
    id: nanoid(),
    path: AppRoute.ONLINE_RESULTS,
    label: 'Онлайн результаты',
    allowedRoles: accessConfig.onlineResults.route,
    icon: IconOnlineResults,
  },
  {
    id: nanoid(),
    path: AppRoute.RESULTS,
    label: 'Результаты',
    allowedRoles: accessConfig.results.route,
    icon: IconResults,
  },
  {
    id: nanoid(),
    path: AppRoute.SHOP,
    label: 'Магазин',
    allowedRoles: accessConfig.shop.route,
    icon: IconShop,
  },
  {
    id: nanoid(),
    path: AppRoute.DOCUMENTS,
    label: 'Документы',
    allowedRoles: accessConfig.documents.route,
    icon: IconDocuments,
  },
  {
    id: nanoid(),
    path: AppRoute.BANNERS,
    label: 'Баннеры',
    allowedRoles: accessConfig.banners.route,
    icon: IconBanners,
  },
  {
    id: nanoid(),
    path: AppRoute.SETTINGS,
    label: 'Настройки',
    allowedRoles: accessConfig.settings.route,
    icon: IconSettings,
  },
  {
    id: nanoid(),
    path: AppRoute.CLUBS,
    label: 'Клубы',
    allowedRoles: accessConfig.clubs.route,
    icon: IconClub,
  },
  {
    id: nanoid(),
    path: AppRoute.LINKS,
    label: 'Ссылки',
    allowedRoles: accessConfig.links.route,
    icon: IconLinks,
  },
  {
    id: nanoid(),
    path: AppRoute.ATHLETES,
    label: 'Спортсмены',
    allowedRoles: accessConfig.athletes.route,
    icon: IconAthletes,
  },
  {
    id: nanoid(),
    path: AppRoute.PAGES,
    label: 'Страницы',
    allowedRoles: accessConfig.pages.route,
    icon: IconPages,
  },
  {
    id: nanoid(),
    path: AppRoute.CALENDAR,
    label: 'Календарь',
    allowedRoles: accessConfig.calendar.route,
    icon: IconCalendar,
  },
  {
    id: nanoid(),
    path: AppRoute.BRANCHES,
    label: 'Отделения',
    allowedRoles: accessConfig.branches.route,
    icon: IconBranches,
  },
  {
    id: nanoid(),
    path: AppRoute.MANAGEMENT,
    label: 'Менеджмент',
    allowedRoles: accessConfig.management.route,
    icon: IconUsers,
  },
  {
    id: nanoid(),
    path: AppRoute.INSURANCES,
    label: 'Страховки',
    allowedRoles: accessConfig.insurances.route,
    icon: IconInsurance,
  },
  {
    id: nanoid(),
    path: AppRoute.OLYMPIC_RESERVE,
    label: 'Олимпийский резерв',
    allowedRoles: accessConfig.olympicReserve.route,
    icon: IconAthletes,
  },
  {
    id: nanoid(),
    path: AppRoute.PROCESSING,
    label: 'Процессинг',
    allowedRoles: accessConfig.processing.route,
    icon: IconProcessing,
  },
  {
    id: nanoid(),
    path: AppRoute.POLLS,
    label: 'Голосования',
    allowedRoles: accessConfig.polls.route,
    icon: IconPolls,
  },
]

export { menuItems }
