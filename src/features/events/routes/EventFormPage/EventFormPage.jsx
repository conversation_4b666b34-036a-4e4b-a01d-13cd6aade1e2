import { useEffect, useState, useRef } from 'react'
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  CardHeader,
  CardTitle,
  Col,
  FloatingLabel,
  Form,
  FormCheck,
  FormControl,
  FormLabel,
  FormSelect,
  Row,
  Tab,
  Tabs,
} from 'react-bootstrap'
import { useForm } from 'react-hook-form'
import { Link, useParams } from 'react-router-dom'

import ImageField from '@/components/Forms/ImageField/ImageField'
import Layout from '@/components/Layout/Layout'

import { AppRoute } from '@/const'
import { useCreateEvent } from '@/features/events/api/createEvent'
import { useGetEvent } from '@/features/events/api/getEvent'
import { useGetEventTypeList } from '@/features/events/api/getEventTypeList'
import { useUpdateEvent } from '@/features/events/api/updateEvent'
import { EventCityForms } from '@/features/events/components/EventCityForms/EventCityForms'
import { useGetProcessingList } from '@/features/processing/api/getProcessingList'
import { getChangedValues } from '@/helpers/getChangedValues'
import { useToast } from '@/hooks/useToast'
import { API_URL } from '@/lib/axios'
import { convertBase64 } from '@/utils/common'

const EventForm = ({ defaultFormData = {} }) => {
  const [certTemplateUploaded, setCertTemplateUploaded] = useState(!!defaultFormData?.result_cert_template)
  const [showCertTemplateField, setShowCertTemplateField] = useState(!!defaultFormData?.result_cert_template)
  const fileInputRef = useRef(null)
  const {
    reset,
    register,
    setValue,
    unregister,
    handleSubmit,
    getValues,
    formState: { errors, dirtyFields },
  } = useForm({
    defaultValues: {
      banners: {
        landing_page: {
          text_color: '#ffffff',
          button_color: '#ff3600',
        },
      },
      enabled: true,
      cancel: true,
      public: true,
    },
  })
  const eventTypeListQuery = useGetEventTypeList()
  const eventTypeList = eventTypeListQuery?.data?.data?.values

  const createEventMutation = useCreateEvent()
  const updateEventMutation = useUpdateEvent(defaultFormData?.public_id)
  const getProcessingListQuery = useGetProcessingList()
  const processingList = getProcessingListQuery?.data?.data?.values
  const loadingStatusProcessing = getProcessingListQuery.status

  const isEdit = Object.keys(defaultFormData).length > 0

  const openToast = useToast()

  useEffect(() => {
    if (isEdit) {
      reset({
        title: defaultFormData?.title,
        title_above: defaultFormData?.title_above,
        processing: {
          public_id: defaultFormData?.processing?.public_id,
        },
        description: defaultFormData?.description,
        public_id: defaultFormData?.public_id,
        external_url: defaultFormData?.external_url,
        video_promo: defaultFormData?.video_promo,
        event_type: {
          public_id: defaultFormData?.event_type?.public_id,
        },
        enabled: defaultFormData?.enabled,
        cancel: defaultFormData?.cancel,
        public: defaultFormData?.public,
        result_cert_template: defaultFormData?.result_cert_template,
        banners: {
          landing_page: {
            text_color: defaultFormData?.banners?.landing_page?.text_color || '',
            button_color: defaultFormData?.banners?.landing_page?.button_color || '',
          },
        },
      })
      setCertTemplateUploaded(!!defaultFormData?.result_cert_template)
      setShowCertTemplateField(!defaultFormData?.result_cert_template)
    }
  }, [defaultFormData, isEdit, reset])

  const handleSubmitFrom = () => {
    const allValues = getValues()

    if (isEdit) {
      const changed = getChangedValues(dirtyFields, allValues, defaultFormData)

      if (Object.keys(changed).length) {
        updateEventMutation.mutate(
          { id: defaultFormData.public_id, data: changed },
          {
            onSuccess: (response) => {
              const updatedEvent = response?.data
              // Обновляем состояние загрузки сертификата
              if (updatedEvent?.result_cert_template) {
                setCertTemplateUploaded(true)
                setShowCertTemplateField(false)
              } else if (updatedEvent?.result_cert_template === null) {
                setCertTemplateUploaded(false)
                setShowCertTemplateField(true)
              }
              // После успешного обновления, сбрасываем форму с новыми данными,
              // чтобы они считались "чистыми" для следующих изменений.
              reset(response.data)
            },
          }
        )
      } else {
        openToast.warning({ message: 'Вы не внесли никаких изменений.' })
      }
    } else {
      createEventMutation.mutate(allValues, {
        onSuccess: (response) => {
          const createdEvent = response?.data
          if (createdEvent?.result_cert_template) {
            setCertTemplateUploaded(true)
            setShowCertTemplateField(false)
          }
        },
      })
    }
  }

  return (
    <Form onSubmit={handleSubmit(handleSubmitFrom)}>
      <Card className="mb-3">
        <CardHeader>
          <CardTitle>Основное</CardTitle>
        </CardHeader>
        <CardBody>
          <Row className="g-2">
            <Col md={4}>
              <Form.Group className="mb-3">
                <FloatingLabel controlId="titleLabel" label="Название">
                  <FormControl
                    {...register('title', {
                      required: Object.keys(defaultFormData)?.length === 0,
                    })}
                    type="text"
                    isInvalid={errors?.title}
                    placeholder=""
                  />
                </FloatingLabel>
              </Form.Group>
            </Col>

            <Col md={4}>
              <Form.Group className="mb-3">
                <FloatingLabel controlId="externalUrlLabel" label="Ссылка">
                  <FormControl
                    {...register('external_url')}
                    type="text"
                    isInvalid={errors?.external_url}
                    placeholder=""
                  />
                </FloatingLabel>
              </Form.Group>
            </Col>

            <Col md={4}>
              {loadingStatusProcessing !== 'loading' ? (
                <Form.Group className="mb-3">
                  <FloatingLabel controlId="processingLabel" label="Юридическое лицо">
                    <FormSelect
                      {...register('processing.public_id', {
                        required: Object.keys(defaultFormData)?.length === 0,
                      })}
                      isInvalid={errors?.processing?.public_id}
                      placeholder=""
                    >
                      <option value="">выберите один из вариантов</option>f
                      {processingList?.map((item, index) => (
                        <option value={item.public_id} key={`${item.public_id}-${index}`}>
                          {item.entity}
                        </option>
                      ))}
                    </FormSelect>
                  </FloatingLabel>
                </Form.Group>
              ) : (
                <Form.Group className="mb-3">
                  <FloatingLabel controlId="processingLabel" label="Юридическое лицо">
                    <FormSelect placeholder="" disabled>
                      <option value="">варианты загружаются</option>
                    </FormSelect>
                  </FloatingLabel>
                </Form.Group>
              )}
            </Col>

            <Col md={6}>
              <Form.Group className="mb-3">
                <FloatingLabel controlId="eventTypePublicIdLabel" label="Тип события">
                  <FormSelect
                    {...register('event_type.public_id', {
                      required: Object.keys(defaultFormData)?.length === 0,
                      disabled: isEdit,
                    })}
                    isInvalid={errors?.event_type?.public_id}
                    placeholder=""
                  >
                    <option value="">выберите один из вариантов</option>
                    {eventTypeList?.map((item) => (
                      <option value={item.public_id} key={item.public_id}>
                        {item.title}
                      </option>
                    ))}
                  </FormSelect>
                </FloatingLabel>
              </Form.Group>
            </Col>

            <Col md={6}>
              <Form.Group className="mb-3">
                <FloatingLabel controlId="publicIdLabel" label="Идентификатор">
                  <FormControl
                    {...register('public_id', {
                      required: Object.keys(defaultFormData)?.length === 0,
                    })}
                    type="text"
                    isInvalid={errors?.public_id}
                    disabled={isEdit}
                    placeholder=""
                  />
                </FloatingLabel>
              </Form.Group>
            </Col>

            <Col md={12}>
              <Form.Group className="mb-3">
                <FloatingLabel controlId="titleAboveLabel" label="Подзаголовок">
                  <FormControl
                    {...register('title_above', {
                      required: Object.keys(defaultFormData)?.length === 0,
                    })}
                    type="text"
                    isInvalid={errors?.title_above}
                    placeholder=""
                  />
                </FloatingLabel>
              </Form.Group>
            </Col>

            <Col md={12}>
              <Form.Group className="mb-3">
                <FloatingLabel controlId="promoVideoLabel" label="Промо видео">
                  <FormControl
                    {...register('video_promo')}
                    type="text"
                    isInvalid={errors?.video_promo}
                    placeholder=""
                  />
                </FloatingLabel>
              </Form.Group>
            </Col>

            <Col md={12}>
              <Form.Group className="mb-3">
                <FloatingLabel controlId="descriptionLabel" label="Примечание">
                  <FormControl
                    {...register('description', {
                      required: Object.keys(defaultFormData)?.length === 0,
                    })}
                    style={{ minHeight: '150px' }}
                    as="textarea"
                    isInvalid={errors?.description}
                    placeholder=""
                  />
                </FloatingLabel>
              </Form.Group>
            </Col>
          </Row>
        </CardBody>
      </Card>

      <Card className="mb-3">
        <CardHeader>
          <CardTitle>Баннеры</CardTitle>
        </CardHeader>
        <CardBody>
          <Row>
            <Col>
              <Row>
                <Col>
                  <center className="mb-2">Для пк</center>
                  <ImageField
                    fieldName="banners.landing_page.desktop_picture"
                    imagePath={defaultFormData?.banners?.landing_page?.desktop_picture}
                    isEdit={isEdit}
                    setValue={setValue}
                    unregister={unregister}
                  />
                </Col>
                <Col>
                  <center className="mb-2">Для телефона</center>
                  <ImageField
                    fieldName="banners.landing_page.mobile_picture"
                    imagePath={defaultFormData?.banners?.landing_page?.mobile_picture}
                    isEdit={isEdit}
                    setValue={setValue}
                    unregister={unregister}
                  />
                </Col>
              </Row>
            </Col>

            <Col md={3}>
              <Row>
                <Col>
                  <FormLabel htmlFor="colorPicker">Цвет текста</FormLabel>
                  <FormControl
                    {...register('banners.landing_page.text_color')}
                    defaultValue=""
                    id="colorPicker_text"
                    type="color"
                    title="Цвет текста"
                  />
                </Col>
                <Col>
                  <FormLabel htmlFor="colorPicker">Цвет кнопки</FormLabel>
                  <FormControl
                    {...register('banners.landing_page.button_color')}
                    id="colorPicker_button"
                    type="color"
                    title="Цвет кнопки"
                  />
                </Col>
              </Row>
            </Col>
          </Row>
        </CardBody>
      </Card>

      <Card className="mb-3">
        <CardBody>
          <Row className="justify-content-around">
            <Col md="auto">
              <FormCheck {...register('enabled')} type="switch" id="activeSwitch" label="Активно" reverse />
            </Col>
            <Col md="auto">
              <FormCheck {...register('cancel')} type="switch" id="cancelSwitch" label="Возвраты" reverse />
            </Col>
            <Col md="auto">
              <FormCheck {...register('public')} type="switch" id="publicSwitch" label="Публичное" reverse />
            </Col>
          </Row>
        </CardBody>
      </Card>

      <Card className="mb-5">
        <CardHeader>
          <CardTitle>Шаблон для сертификата</CardTitle>
        </CardHeader>
        <CardBody>
          <Row>
            <Col md={12}>
              <Form.Group className="mb-3">
                {!showCertTemplateField && defaultFormData?.result_cert_template && (
                  <>
                    <div className="d-flex align-items-center mb-2">
                      <a
                        href={`${API_URL}${defaultFormData.result_cert_template}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="me-3"
                      >
                        Посмотреть загруженный шаблон
                      </a>
                      <Button
                        variant="outline-secondary"
                        size="sm"
                        onClick={() => {
                          setShowCertTemplateField(true)
                          setValue('result_cert_template', null, { shouldDirty: true })
                          setCertTemplateUploaded(false)
                        }}
                      >
                        Обновить/удалить
                      </Button>
                    </div>
                    <Form.Text className="text-success d-block">Шаблон сертификата уже загружен</Form.Text>
                  </>
                )}

                {showCertTemplateField && (
                  <>
                    <div className="d-flex">
                      <FormControl
                        ref={fileInputRef}
                        type="file"
                        accept=".svg,.html"
                        onChange={async (e) => {
                          if (e.target.files?.[0]) {
                            const file = e.target.files[0]
                            try {
                              const base64 = await convertBase64(file)
                              setValue('result_cert_template', base64, { shouldDirty: true })
                              setCertTemplateUploaded(true)
                            } catch {
                              openToast.error({
                                message: 'Ошибка при загрузке файла',
                                duration: 6000,
                              })
                            }
                          }
                        }}
                      />
                      <Button
                        variant="outline-danger"
                        className="ms-2"
                        disabled={!certTemplateUploaded}
                        onClick={() => {
                          unregister('result_cert_template')
                          // Если ранее у события уже был загружен шаблон сертификата,
                          // возвращаемся к начальному состоянию: скрываем поле загрузки
                          // и вновь показываем информацию о текущем шаблоне.
                          if (defaultFormData?.result_cert_template) {
                            setShowCertTemplateField(false)
                            setCertTemplateUploaded(true)
                          } else {
                            setCertTemplateUploaded(false)
                          }
                          // Очищаем инпут файла
                          if (fileInputRef.current) {
                            fileInputRef.current.value = ''
                          }
                        }}
                      >
                        Очистить
                      </Button>
                    </div>
                    {certTemplateUploaded && !defaultFormData?.result_cert_template && (
                      <Form.Text className="text-success d-block">Шаблон сертификата загружен</Form.Text>
                    )}
                    <Form.Text className="text-muted d-block">Загрузите файл в формате SVG или HTML</Form.Text>
                  </>
                )}
              </Form.Group>
            </Col>
          </Row>
        </CardBody>
      </Card>

      <Row>
        <Col />
        <Col md="auto">
          <Button variant="success" disabled={createEventMutation.isLoading} type="submit">
            {isEdit ? 'Сохранить событие' : 'Создать событие'}
          </Button>
        </Col>
        {isEdit && (
          <Col md="auto">
            <Button to={`${AppRoute.EVENTS}/${defaultFormData.public_id}`} variant="outline-secondary" as={Link}>
              К событию
            </Button>
          </Col>
        )}
      </Row>
    </Form>
  )
}

export const EventFormPage = () => {
  const [activeTab, setActiveTab] = useState('main')
  const { eventPublicId } = useParams()

  const getEventQuery = useGetEvent(eventPublicId)
  const event = getEventQuery?.data?.data

  return (
    <Layout title={eventPublicId ? 'Редактирование события' : 'Создание события'}>
      <Tabs className="mb-4 justify-content-center" activeKey={activeTab} onSelect={(key) => setActiveTab(key)}>
        <Tab title="Событие" eventKey="main">
          <EventForm defaultFormData={event} />
        </Tab>
        <Tab title="Города" eventKey="cities" disabled={!eventPublicId}>
          {activeTab === 'cities' && <EventCityForms eventPublicId={eventPublicId} />}
        </Tab>
      </Tabs>
    </Layout>
  )
}
