import { useState } from 'react'
import { But<PERSON>, <PERSON>dal, Row, Col, ListGroup } from 'react-bootstrap'

import { useGenerateTeams } from '../../api/generateTeams'

export function AssignTeamsButton() {
  const [showModal, setShowModal] = useState(false)
  const [results, setResults] = useState({ successful: [], fail: [] })

  const { mutate: generateTeams, isLoading } = useGenerateTeams({
    onSuccess: (data) => {
      setResults(data)
      setShowModal(true)
    },
  })

  return (
    <>
      <Button variant="success" type="button" size="sm" onClick={() => generateTeams()} disabled={isLoading}>
        {isLoading ? 'Присваиваем...' : 'Присвоение команд'}
      </Button>

      <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Результаты присвоения команд</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Row>
            <Col md={6}>
              <h5 className="text-success">Успешно</h5>
              <ListGroup>
                {results?.successful?.map((item, index) => (
                  <ListGroup.Item key={index}>{item}</ListGroup.Item>
                ))}
              </ListGroup>
            </Col>
            <Col md={6}>
              <h5 className="text-danger">Ошибки</h5>
              <ListGroup>
                {results?.fail?.map((item, index) => (
                  <ListGroup.Item key={index}>{item}</ListGroup.Item>
                ))}
              </ListGroup>
            </Col>
          </Row>
        </Modal.Body>
      </Modal>
    </>
  )
}
