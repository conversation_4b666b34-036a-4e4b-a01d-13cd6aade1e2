import moment from 'moment-timezone'
import { useEffect, useRef, useState, useMemo } from 'react'
import { Button, ButtonGroup, Col, Floating<PERSON><PERSON><PERSON>, Form, FormCheck, FormControl, Row } from 'react-bootstrap'
import { useForm, useFieldArray, Controller } from 'react-hook-form'

import { TimezoneDisplay } from '@/components/TimezoneDisplay'

import { useCreateEventCityFormat } from '@/features/events/api/createEventCityFormat'
import { useUpdateEventCityFormat } from '@/features/events/api/updateEventCityFormat'
import { AgesField } from '@/features/events/components/AgesField/AgesField'
import { AssignTeamsButton } from '@/features/events/components/AssignTeamsButton/AssignTeamsButton'
import { CompleteTeamsButton } from '@/features/events/components/CompleteTeamsButton/CompleteTeamsButton'
import { DistanceField } from '@/features/events/components/DistanceField/DistanceField'
import { FormatFormSwitches } from '@/features/events/components/FormatFormSwitches/FormatFormSwitches'
import { MerchField } from '@/features/events/components/MerchField/MerchField'
import { RequirementField } from '@/features/events/components/RequirementField/RequirementField'
import { useGetInsuranceTypes } from '@/features/insurances/api/getInsuranceTypes'
import { useToast } from '@/hooks/useToast'
import { Tag } from '@/pages/ShopScreen/components/Delivery/Delivery'
import { cleanUnchangedArrays, removeEmptyString2 } from '@/utils/common'
import { unixToMoment } from '@/utils/date'
import { checkSetValue } from '@/utils/forms'

const filterDistances = (distances) => {
  if (!distances || !Array.isArray(distances) || distances.length === 0) {
    return distances
  }

  return distances.map((item) => {
    if (item.sport) {
      const { public_id } = item.sport
      return {
        ...item,
        sport: {
          public_id,
        },
      }
    }
    return item
  })
}

export const FormatForm = ({ city, defaultFormData, copyFormData, currentTimezone, onCloseModal }) => {
  const defaultValues = useMemo(
    () => ({
      title: defaultFormData?.title,
      address: defaultFormData?.address,
      public_id: defaultFormData?.public_id,
      description: defaultFormData?.description,
      start_number: defaultFormData?.start_number,
      buy_limit: defaultFormData?.buy_limit,
      enabled: defaultFormData?.enabled,
      hidden: defaultFormData?.hidden,
      preregistration: defaultFormData?.preregistration,
      clubs: defaultFormData?.clubs,
      registration: defaultFormData?.registration,
      shirt_unavailable: defaultFormData?.shirt_unavailable,
      license_file: defaultFormData?.license_file,
      qr: defaultFormData?.qr,
      start_team_number: defaultFormData?.start_team_number,
      team_count: defaultFormData?.team_count,
      max_count: defaultFormData?.max_count,
      team: defaultFormData?.team,
      team_all: defaultFormData?.team_all,
      prices: defaultFormData?.prices || [],
      merch: defaultFormData?.merch || [],
      requirements: defaultFormData?.requirements || [],
      shop: defaultFormData?.shop || [],
      notes: defaultFormData?.notes || [],
      clusters: defaultFormData?.clusters || [],
      additional_fields: defaultFormData?.additional_fields || [],
      insurances: defaultFormData?.insurances || [],
      distances: filterDistances(defaultFormData?.distances) || [],
      age_min: defaultFormData?.age_min,
      age_max: defaultFormData?.age_max,
      ages: {
        method: defaultFormData?.ages?.method,
        values: defaultFormData?.ages?.values || [],
      },
      _showRegions: defaultFormData?.regions ? true : false,
      regions: defaultFormData?.regions || null,
      limits: {
        athlete: defaultFormData?.limits?.athlete || '',
        corporate: defaultFormData?.limits?.corporate || '',
        partner: defaultFormData?.limits?.partner || '',
        brand: defaultFormData?.limits?.brand || '',
        sport: defaultFormData?.limits?.sport || '',
        other: defaultFormData?.limits?.other || '',
      },
    }),
    [defaultFormData]
  )

  const copyValues = useMemo(
    () => ({
      title: copyFormData?.title,
      address: copyFormData?.address,
      public_id: copyFormData?.public_id,
      description: copyFormData?.description,
      start_number: copyFormData?.start_number,
      buy_limit: copyFormData?.buy_limit,
      enabled: copyFormData?.enabled,
      hidden: copyFormData?.hidden,
      preregistration: copyFormData?.preregistration,
      clubs: copyFormData?.clubs,
      registration: copyFormData?.registration,
      shirt_unavailable: copyFormData?.shirt_unavailable,
      license_file: copyFormData?.license_file,
      qr: copyFormData?.qr,
      start_team_number: copyFormData?.start_team_number,
      team_count: copyFormData?.team_count,
      max_count: copyFormData?.max_count,
      team: copyFormData?.team,
      team_all: copyFormData?.team_all,
      prices: copyFormData?.prices || [],
      merch: copyFormData?.merch || [],
      requirements: copyFormData?.requirements || [],
      shop: copyFormData?.shop || [],
      notes: copyFormData?.notes || [],
      clusters: copyFormData?.clusters || [],
      additional_fields: copyFormData?.additional_fields || [],
      insurances: copyFormData?.insurances || [],
      distances: filterDistances(copyFormData?.distances) || [],
      age_min: copyFormData?.age_min,
      age_max: copyFormData?.age_max,
      ages: {
        method: copyFormData?.ages?.method,
        values: copyFormData?.ages?.values || [],
      },
      _showRegions: copyFormData?.regions ? true : false,
      regions: copyFormData?.regions || null,
      start_time: unixToMoment(copyFormData?.start_time).format('YYYY-MM-DDTHH:mm:ss') || '',
      last_refund_date: unixToMoment(copyFormData?.last_refund_date).format('YYYY-MM-DDTHH:mm:ss') || '',
      limits: {
        athlete: copyFormData?.limits?.athlete || '',
        corporate: copyFormData?.limits?.corporate || '',
        partner: copyFormData?.limits?.partner || '',
        brand: copyFormData?.limits?.brand || '',
        sport: copyFormData?.limits?.sport || '',
        other: copyFormData?.limits?.other || '',
      },
    }),
    [copyFormData]
  )

  const isEdit = Object.keys(defaultFormData).length > 0
  const isCopy = Object.keys(copyFormData).length > 0

  const {
    register,
    handleSubmit,
    control,
    setValue,
    watch,
    formState: { errors },
    reset,
  } = useForm({
    defaultValues: isCopy ? copyValues : defaultValues,
  })

  const [pricesVariant, setPricesVariant] = useState('dates')

  const createEventCityFormatMutation = useCreateEventCityFormat(city.public_id, onCloseModal)
  const updateEventCityFormatMutation = useUpdateEventCityFormat(defaultFormData?.event_city?.public_id, onCloseModal)
  const getInsuranceTypesQuery = useGetInsuranceTypes()

  const insuranceTypes = getInsuranceTypesQuery?.data?.data?.values

  const openToast = useToast()

  const lastRefundDateRef = useRef(null)

  const {
    fields: insuranceFields,
    append: appendInsurance,
    remove: removeInsurance,
  } = useFieldArray({
    control,
    name: 'insurances',
  })

  const {
    fields: priceFields,
    append: appendPrice,
    remove: removePrice,
  } = useFieldArray({
    control,
    name: 'prices',
  })

  const {
    fields: shopFields,
    append: appendShop,
    remove: removeShop,
  } = useFieldArray({
    control,
    name: 'shop',
  })

  const {
    fields: noteFields,
    append: appendNote,
    remove: removeNote,
  } = useFieldArray({
    control,
    name: 'notes',
  })

  const {
    fields: clusterFields,
    append: appendCluster,
    remove: removeCluster,
  } = useFieldArray({
    control,
    name: 'clusters',
  })

  const {
    fields: additionalFields,
    append: appendAdditional,
    remove: removeAdditional,
  } = useFieldArray({
    control,
    name: 'additional_fields',
  })

  useEffect(() => {
    const data = isCopy ? copyFormData : defaultValues
    if (data?.prices?.[0] && Object.prototype.hasOwnProperty.call(data.prices[0], 'tickets_count')) {
      setPricesVariant('tickets')
    }

    if (!isEdit && !isCopy) {
      const lastRefundDateDefault = city?.start_time
        ? unixToMoment(city.start_time).add(5, 'days').format('YYYY-MM-DDTHH:mm:ss')
        : undefined

      const resetValues = {
        buy_limit: 100,
        enabled: false,
        hidden: false,
        preregistration: false,
        clubs: false,
        registration: false,
        shirt_unavailable: false,
        license_file: false,
        qr: false,
        limits: {
          athlete: 0,
          corporate: 0,
          partner: 0,
          brand: 0,
          sport: 0,
          other: 0,
        },
      }

      if (lastRefundDateDefault) {
        resetValues.last_refund_date = lastRefundDateDefault
        lastRefundDateRef.current.value = lastRefundDateDefault
      }

      reset(resetValues)

      appendPrice({
        price: '',
        start_date: '',
        end_date: '',
      })
    }

    if (isCopy) {
      const lastRefundDateDefault = city?.start_time
        ? unixToMoment(city.start_time).add(5, 'days').format('YYYY-MM-DDTHH:mm:ss')
        : undefined

      if (lastRefundDateDefault) {
        setValue('last_refund_date', lastRefundDateDefault)

        lastRefundDateRef.current.value = lastRefundDateDefault
      }
    }

    if (isEdit && defaultFormData?.prices?.length === 0) {
      appendPrice({
        price: '',
        start_date: '',
        end_date: '',
      })
    }
  }, [defaultFormData, appendPrice, city.start_time, copyFormData, defaultValues, isCopy, isEdit, reset, setValue])

  function cleanUnchangedBooleans(formData, defaultFormData) {
    const cleanedData = { ...formData }

    for (const key in formData) {
      if (Object.prototype.hasOwnProperty.call(formData, key)) {
        const value = formData[key]
        const defaultValue = defaultFormData[key]

        if (typeof value === 'boolean') {
          if (value === defaultValue) {
            delete cleanedData[key]
          }
        }
      }
    }

    return cleanedData
  }

  function filterPrices(formData, type) {
    if (!formData || !formData.prices || !Array.isArray(formData.prices)) {
      return formData
    }

    const processedPrices = formData.prices.map((price) => {
      const { start_date, end_date, tickets_count, ...rest } = price

      if (type === 'tickets') {
        return { ...rest, tickets_count }
      } else if (type === 'dates') {
        return { ...rest, start_date, end_date }
      }

      return price
    })

    return {
      ...formData,
      prices: processedPrices,
    }
  }

  function cleanUnchangedAges(formData, defaultFormData) {
    if (!formData.ages || !defaultFormData.ages) {
      return formData
    }

    const result = { ...formData }

    if (result.ages.method === defaultFormData.ages.method) {
      if (Object.prototype.hasOwnProperty.call(result.ages, 'method')) {
        result.ages = { ...result.ages }
        delete result.ages.method
      }
    }

    const withoutValues = cleanUnchangedArrays(result, defaultFormData, ['ages.values'])

    if (withoutValues.ages && Object.keys(withoutValues.ages).length === 0) {
      delete withoutValues.ages
    }

    return withoutValues
  }

  const handleClickSubmit = (data) => {
    const filteredData = removeEmptyString2(data, defaultFormData)

    if (Object.prototype.hasOwnProperty.call(filteredData, '_showRegions')) {
      filteredData.regions = filteredData._showRegions ? [] : null

      const isSameValue = filteredData._showRegions === Boolean(defaultFormData?.regions)
      if (isSameValue) {
        delete filteredData.regions
      }

      delete filteredData._showRegions
    }

    if (isEdit) {
      const arrayKeys = [
        'clusters',
        'prices',
        'merch',
        'requirements',
        'shop',
        'notes',
        'additional_fields',
        'insurances',
        'distances',
      ]
      const filteredData2 = cleanUnchangedArrays(filteredData, defaultFormData, arrayKeys)
      const filteredData3 = cleanUnchangedBooleans(filteredData2, defaultFormData)
      const filteredData4 = cleanUnchangedAges(filteredData3, defaultFormData)
      const filteredData5 = filterPrices(filteredData4, pricesVariant)

      if (Object.keys(filteredData5)?.length > 0) {
        updateEventCityFormatMutation.mutate({ id: defaultFormData.public_id, data: filteredData5 })
      } else {
        openToast.warning({ message: 'Вы не внесли никаких изменений.' })
      }
    } else {
      const arrayKeys = [
        'clusters',
        'prices',
        'merch',
        'requirements',
        'shop',
        'notes',
        'additional_fields',
        'insurances',
        'distances',
      ]
      const filteredData2 = cleanUnchangedArrays(filteredData, {}, arrayKeys)
      const filteredData3 = filterPrices(filteredData2, pricesVariant)
      filteredData3.event_public_id = city.event_public_id
      filteredData3.city = {
        id: 0,
      }
      filteredData3.city.id = city.city.id
      filteredData3.event_city = {
        public_id: city.public_id,
      }

      createEventCityFormatMutation.mutate(filteredData3)
    }
  }

  const handleAppendCluster = () => {
    appendCluster({
      label: '',
      value: '',
      file: false,
    })
  }

  const handleAppendAdditionalField = () => {
    appendAdditional({
      label: '',
      name: '',
      required: true,
    })
  }

  const handleAppendPriceField = () => {
    const price = {
      price: '',
      start_date: '',
      end_date: '',
      tickets_count: '',
    }

    appendPrice(price)
  }

  const handleSelectInsurance = (evt) => {
    const public_id = evt.target.value

    if (public_id !== '') {
      appendInsurance({ public_id })
    }
  }

  const handleChangeDate = (evt) => {
    const name = evt.target.name
    const value = evt.target.value
    const formatValue = moment.tz(value, currentTimezone).utc().toISOString()

    setValue(name, formatValue)
  }

  const getValueByPath = (obj, path) => {
    return path.split('.').reduce((acc, key) => (acc && acc[key] ? acc[key] : undefined), obj)
  }

  const returnDefaultDate = (key) => {
    const data = isCopy ? copyFormData : defaultFormData
    const value = getValueByPath(data, key)

    if (value) {
      const timezone = defaultFormData.timezone || moment.tz.guess()
      const timestamp = unixToMoment(value)

      if (timestamp.isValid()) {
        return timestamp.tz(timezone).format('YYYY-MM-DDTHH:mm:ss')
      }
    }

    return ''
  }

  const returnInsuranceName = (id) => {
    const findInsurance = insuranceTypes?.find((insurance) => insurance.public_id === id)

    return findInsurance?.name || id
  }

  const handleTogglePriceTickets = (key) => {
    if (key !== pricesVariant) {
      setPricesVariant(key)
    }
  }

  const validatePublicId = (value) => {
    if (!isEdit) {
      if (value !== '') {
        if (isCopy && copyValues.public_id === value) {
          return false
        }

        return true
      }
    }

    return true
  }

  const handleChangeTeam = (onChange, value) => {
    onChange(value)
    setValue('team_all', value)
    if (!value) {
      setValue('start_team_number', undefined)
      setValue('team_count', undefined)
      setValue('max_count', undefined)
    }
  }

  const handleChangeTeamAll = (onChange, variant) => {
    if (variant === 'all') {
      onChange(true)
    } else {
      onChange(false)
    }
  }

  return (
    <Form onSubmit={handleSubmit(handleClickSubmit)}>
      {/* Отображение часового пояса и времени */}
      <TimezoneDisplay timezone={currentTimezone} className="mb-3" />

      <Row>
        <Col>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="titleLabel" label="Название">
              <FormControl
                {...register('title', {
                  required: !isEdit,
                  setValueAs: (v) => checkSetValue(v, defaultFormData?.title, 'text'),
                })}
                type="text"
                isInvalid={errors?.title}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        <Col>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="addressLabel" label="Адрес">
              <FormControl
                {...register('address', {
                  setValueAs: (v) => checkSetValue(v, defaultFormData?.address, 'text'),
                })}
                type="text"
                isInvalid={errors?.address}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        <Col>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="publicIdLabel" label="Идентификатор">
              <FormControl
                {...register('public_id', {
                  required: !isEdit,
                  validate: validatePublicId,
                  setValueAs: (v) => checkSetValue(v, defaultFormData?.public_id, 'text'),
                })}
                type="text"
                isInvalid={errors?.public_id}
                disabled={isEdit}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
      </Row>

      <Row>
        <Col>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="startNumberLabel" label="Стартовый номер">
              <FormControl
                {...register('start_number', {
                  setValueAs: (v) => checkSetValue(v, defaultFormData?.start_number, 'number'),
                })}
                type="number"
                isInvalid={errors?.start_number}
                onWheel={(e) => e.target.blur()}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        <Col>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="buyLimitLabel" label="Доступно для 1 юзера">
              <FormControl
                {...register('buy_limit', {
                  setValueAs: (v) => checkSetValue(v, defaultFormData?.buy_limit, 'number'),
                })}
                type="number"
                isInvalid={errors?.buy_limit}
                onWheel={(e) => e.target.blur()}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        <Col>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="startDateLabel" label="Дата старта">
              <FormControl
                onChange={handleChangeDate}
                defaultValue={returnDefaultDate('start_time')}
                type="datetime-local"
                name={'start_time'}
                isInvalid={errors?.start_time}
                placeholder=""
                required
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        <Col>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="lastRefundDateLabel" label="Закрытие возвратов">
              <FormControl
                onChange={handleChangeDate}
                defaultValue={returnDefaultDate('last_refund_date')}
                type="datetime-local"
                name={'last_refund_date'}
                isInvalid={errors?.last_refund_date}
                ref={lastRefundDateRef}
                required={!isEdit}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
      </Row>

      <Row>
        <Col>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="descriptionLabel" label="Описание">
              <FormControl
                {...register('description', {
                  required: Object.keys(defaultFormData)?.length === 0,
                  setValueAs: (v) => checkSetValue(v, defaultFormData?.description, 'text'),
                })}
                style={{ minHeight: '150px' }}
                as="textarea"
                isInvalid={errors?.description}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
      </Row>

      <hr style={{ color: '#dcdcdf' }} />

      <FormatFormSwitches
        register={register}
        city={city}
        defaultFormData={defaultFormData}
        watch={watch}
        setValue={setValue}
        control={control}
      />

      <hr style={{ color: '#dcdcdf' }} />

      <h5>Лимиты</h5>
      <Row>
        <Col>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="limitsAthleteLabel" label="Физики">
              <FormControl
                {...register('limits.athlete', {
                  setValueAs: (v) => checkSetValue(v, defaultFormData?.limits?.athlete, 'number'),
                })}
                type="number"
                isInvalid={errors?.limits?.athlete}
                onWheel={(e) => e.target.blur()}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        <Col>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="limitsCorporateLabel" label="Корпы">
              <FormControl
                {...register('limits.corporate', {
                  setValueAs: (v) => checkSetValue(v, defaultFormData?.limits?.corporate, 'number'),
                })}
                type="number"
                isInvalid={errors?.limits?.corporate}
                onWheel={(e) => e.target.blur()}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        <Col>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="limitsPartnerLabel" label="Партнёры">
              <FormControl
                {...register('limits.partner', {
                  setValueAs: (v) => checkSetValue(v, defaultFormData?.limits?.partner, 'number'),
                })}
                type="number"
                isInvalid={errors?.limits?.partner}
                onWheel={(e) => e.target.blur()}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        <Col>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="limitsBrandLabel" label="Бренд">
              <FormControl
                {...register('limits.brand', {
                  setValueAs: (v) => checkSetValue(v, defaultFormData?.limits?.brand, 'number'),
                })}
                type="number"
                isInvalid={errors?.limits?.brand}
                onWheel={(e) => e.target.blur()}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        <Col>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="limitsSportLabel" label="Спорт">
              <FormControl
                {...register('limits.sport', {
                  setValueAs: (v) => checkSetValue(v, defaultFormData?.limits?.sport, 'number'),
                })}
                type="number"
                isInvalid={errors?.limits?.sport}
                onWheel={(e) => e.target.blur()}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        <Col>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="limitsOtherLabel" label="Другие">
              <FormControl
                {...register('limits.other', {
                  setValueAs: (v) => checkSetValue(v, defaultFormData?.limits?.other, 'number'),
                })}
                type="number"
                isInvalid={errors?.limits?.other}
                onWheel={(e) => e.target.blur()}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
      </Row>

      <hr style={{ color: '#dcdcdf' }} />

      <Row>
        <Col md="auto">
          <Controller
            control={control}
            name="team"
            render={({ field: { onChange, value, ref } }) => (
              <FormCheck
                type="switch"
                id="teamSwitch"
                label="Команда"
                checked={value}
                onChange={(e) => handleChangeTeam(onChange, e.target.checked)}
                ref={ref}
                reverse
              />
            )}
          />
        </Col>
        {watch('team') && (
          <>
            <Col md="auto">
              <Controller
                control={control}
                name="team_all"
                render={({ field: { onChange, value, ref } }) => (
                  <FormCheck
                    type="radio"
                    id="teamAllSwitch"
                    label="Команда целиком"
                    checked={value}
                    onChange={(e) => handleChangeTeamAll(onChange, e.target.checked, 'all')}
                    ref={ref}
                    reverse
                  />
                )}
              />
            </Col>
            <Col md="auto">
              <Controller
                control={control}
                name="team_all"
                render={({ field: { onChange, value, ref } }) => (
                  <FormCheck
                    type="radio"
                    id="teamPositionSwitch"
                    label="Место в команде"
                    checked={!value}
                    onChange={(e) => handleChangeTeamAll(onChange, e.target.checked, 'position')}
                    ref={ref}
                    reverse
                  />
                )}
              />
            </Col>
            <Col md="auto">
              <AssignTeamsButton />
            </Col>
            <Col md="auto">
              <CompleteTeamsButton formatPublicId={defaultFormData?.public_id} />
            </Col>

            <Col md="12">
              <hr style={{ color: '#dcdcdf' }} />

              <h5>Команда</h5>
              <Row>
                <Col>
                  <Form.Group className="mb-3">
                    <FloatingLabel controlId="startTeamNumberLabel" label="Стартовый номер команды">
                      <FormControl
                        {...register('start_team_number', {
                          setValueAs: (v) => checkSetValue(v, defaultFormData?.start_team_number, 'number'),
                        })}
                        type="number"
                        isInvalid={errors?.start_team_number}
                        onWheel={(e) => e.target.blur()}
                        placeholder=""
                      />
                    </FloatingLabel>
                  </Form.Group>
                </Col>
                <Col>
                  <Form.Group className="mb-3">
                    <FloatingLabel controlId="teamCountLabel" label="Количество команд">
                      <FormControl
                        {...register('team_count', {
                          setValueAs: (v) => checkSetValue(v, defaultFormData?.team_count, 'number'),
                        })}
                        type="number"
                        isInvalid={errors?.team_count}
                        onWheel={(e) => e.target.blur()}
                        placeholder=""
                      />
                    </FloatingLabel>
                  </Form.Group>
                </Col>
                <Col>
                  <Form.Group className="mb-3">
                    <FloatingLabel controlId="maxCountLabel" label="Максимальное количество">
                      <FormControl
                        {...register('max_count', {
                          setValueAs: (v) => checkSetValue(v, defaultFormData?.max_count, 'number'),
                        })}
                        type="number"
                        isInvalid={errors?.max_count}
                        onWheel={(e) => e.target.blur()}
                        placeholder=""
                      />
                    </FloatingLabel>
                  </Form.Group>
                </Col>
              </Row>
            </Col>
          </>
        )}
      </Row>

      <hr style={{ color: '#dcdcdf' }} />

      <h5>Возрастные ограничения</h5>
      <Row className="mb-3">
        <Col md={3}>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="ageMinLabel" label="Минимальный возраст">
              <FormControl
                {...register('age_min', {
                  setValueAs: (v) => checkSetValue(v, defaultFormData?.age_min, 'number'),
                })}
                type="number"
                isInvalid={errors?.age_min}
                onWheel={(e) => e.target.blur()}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        <Col md={3}>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="ageMaxLabel" label="Максимальный возраст">
              <FormControl
                {...register('age_max', {
                  setValueAs: (v) => checkSetValue(v, defaultFormData?.age_max, 'number'),
                })}
                type="number"
                isInvalid={errors?.age_max}
                onWheel={(e) => e.target.blur()}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
      </Row>

      <hr style={{ color: '#dcdcdf' }} />

      <AgesField control={control} register={register} errors={errors} defaultValues={defaultFormData?.ages} />

      <hr style={{ color: '#dcdcdf' }} />

      <h5>
        Дополнительные поля{' '}
        <Button onClick={handleAppendAdditionalField} variant="link" type="button" size="sm">
          <i className="bi bi-plus-circle me-2" />
        </Button>
      </h5>
      {additionalFields.map((item, index) => (
        <Row className="mb-3 g-3 align-items-center" key={index}>
          <Col>
            <Form.Group>
              <FloatingLabel controlId={`clusters.${index}.labelLabel`} label="Название поля">
                <FormControl
                  {...register(`additional_fields.${index}.label`)}
                  type="text"
                  isInvalid={errors?.additional_fields?.[index]?.label}
                  placeholder=""
                />
              </FloatingLabel>
            </Form.Group>
          </Col>
          <Col>
            <Form.Group>
              <FloatingLabel controlId={`additional_fields.${index}.nameLabel`} label="Ключ поля (только латиница)">
                <FormControl
                  {...register(`additional_fields.${index}.name`)}
                  type="text"
                  isInvalid={errors?.additional_fields?.[index]?.name}
                  placeholder=""
                />
              </FloatingLabel>
            </Form.Group>
          </Col>

          <Col md="auto">
            <FormCheck
              {...register(`additional_fields.${index}.required`)}
              type="switch"
              id={`additional_fields.${index}.requiredSwitch`}
              label="Обязательное"
              reverse
            />
          </Col>

          <Col md="auto">
            <Button onClick={() => removeAdditional(index)} variant="outline-danger" type="button" size="sm">
              <i className="bi bi-trash3-fill" />
            </Button>
          </Col>
        </Row>
      ))}

      <hr style={{ color: '#dcdcdf' }} />

      <h5>Виды страховок</h5>
      <Row className="mb-3">
        <Col md="auto">
          <FormControl as="select" onChange={handleSelectInsurance}>
            <option value="">выберите один из вариантов</option>
            {insuranceTypes
              ?.filter((el) => !insuranceFields.some((field) => field.public_id === el.public_id))
              .map((item) => (
                <option value={item.public_id} key={item.public_id}>
                  {item.name}
                </option>
              ))}
          </FormControl>
        </Col>
        <Col md="auto"></Col>
      </Row>
      <p
        style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '20px',
        }}
      >
        <span>
          {insuranceFields.map((field, index) => (
            <Tag onClick={() => removeInsurance(index)} key={field?.public_id}>
              {returnInsuranceName(field.public_id)}
            </Tag>
          ))}
        </span>
      </p>

      <hr style={{ color: '#dcdcdf' }} />

      <Row>
        <Col>
          <h5>
            Цены и график повышения{' '}
            <Button onClick={handleAppendPriceField} variant="link" type="button" size="sm">
              <i className="bi bi-plus-circle me-2" />
            </Button>
          </h5>
        </Col>
        <Col md="auto">
          <ButtonGroup>
            <Button
              onClick={() => handleTogglePriceTickets('dates')}
              variant={pricesVariant === 'dates' ? 'primary' : 'light'}
              type="button"
              size="sm"
            >
              По датам
            </Button>
            <Button
              onClick={() => handleTogglePriceTickets('tickets')}
              variant={pricesVariant === 'tickets' ? 'primary' : 'light'}
              type="button"
              size="sm"
            >
              По билетам
            </Button>
          </ButtonGroup>
        </Col>
      </Row>
      {priceFields.map((item, index) => (
        <Row className="mb-3 g-3 align-items-center" key={index}>
          <Col>
            <Form.Group>
              <FloatingLabel controlId={`prices.${index}.labelLabel`} label="Цена">
                <FormControl
                  {...register(`prices.${index}.price`, {
                    required: true,
                    setValueAs: (v) => checkSetValue(v, {}, 'number'),
                  })}
                  type="number"
                  isInvalid={errors?.prices?.[index]?.price}
                  onWheel={(e) => e.target.blur()}
                  placeholder=""
                />
              </FloatingLabel>
            </Form.Group>
          </Col>
          {pricesVariant === 'dates' && (
            <>
              <Col>
                <Form.Group>
                  <FloatingLabel controlId="pricesStartDateLabel" label="Начало">
                    <FormControl
                      onChange={handleChangeDate}
                      defaultValue={returnDefaultDate(`prices.${index}.start_date`)}
                      type="datetime-local"
                      name={`prices.${index}.start_date`}
                      isInvalid={errors?.prices?.[index]?.start_date}
                      required
                      step={1}
                      placeholder=""
                    />
                  </FloatingLabel>
                </Form.Group>
              </Col>
              <Col>
                <Form.Group>
                  <FloatingLabel controlId="pricesEndDateLabel" label="Конец">
                    <FormControl
                      onChange={handleChangeDate}
                      defaultValue={returnDefaultDate(`prices.${index}.end_date`)}
                      type="datetime-local"
                      name={`prices.${index}.end_date`}
                      isInvalid={errors?.prices?.[index]?.end_date}
                      required
                      step={1}
                      placeholder=""
                    />
                  </FloatingLabel>
                </Form.Group>
              </Col>
            </>
          )}

          {pricesVariant === 'tickets' && (
            <Col>
              <Form.Group>
                <FloatingLabel controlId={`prices.${index}.labelLabel`} label="Количество билетов">
                  <FormControl
                    {...register(`prices.${index}.tickets_count`, {
                      setValueAs: (v) => checkSetValue(v, {}, 'number'),
                      required: true,
                    })}
                    type="number"
                    isInvalid={errors?.prices?.[index]?.tickets_count}
                    onWheel={(e) => e.target.blur()}
                    placeholder=""
                  />
                </FloatingLabel>
              </Form.Group>
            </Col>
          )}

          <Col md="auto">
            <Button
              onClick={() => removePrice(index)}
              variant="outline-danger"
              type="button"
              size="sm"
              disabled={priceFields.length === 1}
            >
              <i className="bi bi-trash3-fill" />
            </Button>
          </Col>
        </Row>
      ))}

      <hr style={{ color: '#dcdcdf' }} />

      <MerchField control={control} register={register} errors={errors} />

      <hr style={{ color: '#dcdcdf' }} />

      <RequirementField control={control} register={register} errors={errors} />

      <hr style={{ color: '#dcdcdf' }} />

      <h5>
        Магазин{' '}
        <Button onClick={() => appendShop('')} variant="link" type="button" size="sm">
          <i className="bi bi-plus-circle me-2" />
        </Button>
      </h5>
      <Row className="g-3">
        {shopFields.map((index) => (
          <Col md="4" key={index}>
            <Row className="g-1 align-items-center">
              <Col>
                <Form.Group>
                  <FormControl
                    {...register(`shop.${index}`)}
                    type="text"
                    isInvalid={errors?.shop?.[index]}
                    placeholder=""
                  />
                </Form.Group>
              </Col>

              <Col md="auto">
                <Button onClick={() => removeShop(index)} variant="outline-danger" type="button" size="sm">
                  <i className="bi bi-trash3-fill" />
                </Button>
              </Col>
            </Row>
          </Col>
        ))}
      </Row>

      <hr style={{ color: '#dcdcdf' }} />

      <h5>
        Заметки{' '}
        <Button onClick={() => appendNote('')} variant="link" type="button" size="sm">
          <i className="bi bi-plus-circle me-2" />
        </Button>
      </h5>
      <Row className="g-3">
        {noteFields.map((item, index) => (
          <Col md="12" key={index}>
            <Row className="g-1 align-items-center">
              <Col>
                <Form.Group>
                  <FormControl
                    {...register(`notes.${index}`, { required: true })}
                    type="text"
                    isInvalid={errors?.notes?.[index]}
                    placeholder=""
                  />
                </Form.Group>
              </Col>

              <Col md="auto">
                <Button onClick={() => removeNote(index)} variant="outline-danger" type="button" size="sm">
                  <i className="bi bi-trash3-fill" />
                </Button>
              </Col>
            </Row>
          </Col>
        ))}
      </Row>

      <hr style={{ color: '#dcdcdf' }} />

      <h5>
        Кластеры{' '}
        <Button onClick={handleAppendCluster} variant="link" type="button" size="sm">
          <i className="bi bi-plus-circle me-2" />
        </Button>
      </h5>
      {clusterFields.map((item, index) => (
        <Row className="mb-3 g-3 align-items-center" key={index}>
          <Col>
            <Form.Group>
              <FloatingLabel controlId={`clusters.${index}.labelLabel`} label="Название">
                <FormControl
                  {...register(`clusters.${index}.label`)}
                  type="text"
                  isInvalid={errors?.clusters?.[index]?.label}
                  placeholder=""
                />
              </FloatingLabel>
            </Form.Group>
          </Col>
          <Col>
            <Form.Group>
              <FloatingLabel controlId={`clusters.${index}.valueLabel`} label="Кластер">
                <FormControl
                  {...register(`clusters.${index}.value`)}
                  type="text"
                  isInvalid={errors?.clusters?.[index]?.value}
                  placeholder=""
                />
              </FloatingLabel>
            </Form.Group>
          </Col>

          <Col md="auto">
            <Controller
              control={control}
              name={`clusters.${index}.file`}
              render={({ field: { onChange, value, ref } }) => (
                <FormCheck
                  type="checkbox"
                  id={`clusterFileCheckbox${index}`}
                  label="Документ загружен"
                  checked={value}
                  onChange={(e) => onChange(e.target.checked)}
                  ref={ref}
                />
              )}
            />
          </Col>

          <Col md="auto">
            <Button onClick={() => removeCluster(index)} variant="outline-danger" type="button" size="sm">
              <i className="bi bi-trash3-fill" />
            </Button>
          </Col>
        </Row>
      ))}

      <hr style={{ color: '#dcdcdf' }} />

      <DistanceField control={control} register={register} errors={errors} />

      <hr style={{ color: '#dcdcdf' }} />

      <Row>
        <Col />
        <Col md="auto">
          <Button type="submit">{isEdit ? 'Сохранить' : 'Добавить'}</Button>
        </Col>
      </Row>
    </Form>
  )
}
