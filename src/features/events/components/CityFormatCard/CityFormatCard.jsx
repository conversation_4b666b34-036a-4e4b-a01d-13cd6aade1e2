import { useState } from 'react'
import { <PERSON>ge, Button, Card, Col, OverlayTrigger, Row, Tooltip } from 'react-bootstrap'
import { Link } from 'react-router-dom'

import { unixToMoment } from '@/utils/date'

import styles from './CityFormatCard.module.scss'

export const CityFormatCard = ({ item }) => {
  const [copySuccess, setCopySuccess] = useState(false)
  const hasInsurance = item.insurance_available

  const getAgeGroups = () => {
    if (!item?.ages?.values || item?.ages?.values?.length === 0) return 'Не указано'

    return item.ages.values.map((age) => `${age.min}-${age.max}`).join(', ')
  }

  const getAgeMethod = () => {
    if (item?.ages?.method === 'day') return 'День старта'
    if (item?.ages?.method === 'year') return 'Конец года'
    return item?.ages?.method
  }

  const getTotalLimit = () => {
    if (!item.limits) return 0
    const { athlete, corporate, partner, other, brand, sport } = item.limits
    return (athlete || 0) + (corporate || 0) + (partner || 0) + (other || 0) + (brand || 0) + (sport || 0)
  }

  const getDate = (date) => {
    return `${unixToMoment(date).tz(item.timezone).format('DD.MM.YYYY, HH:mm')}`
  }

  const copyToClipboard = () => {
    const url = `${window.location.origin}/manager/city-format/${item.public_id}`
    navigator.clipboard.writeText(url).then(() => {
      setCopySuccess(true)
      setTimeout(() => setCopySuccess(false), 2000)
    })
  }

  const renderTooltip = (props) => (
    <Tooltip id="copy-tooltip" {...props}>
      {copySuccess ? 'Скопировано!' : 'Копировать ссылку'}
    </Tooltip>
  )

  return (
    <Card>
      <Card.Header className={`${styles.cardHeader} d-flex justify-content-between align-items-center`}>
        <h5 className="mb-0">{item.title}</h5>
        <div>
          {item.enabled ? (
            <Badge bg="success" className="me-1">
              Включен
            </Badge>
          ) : (
            <Badge bg="secondary" className="me-1">
              Выключен
            </Badge>
          )}
          {item.hidden && (
            <Badge bg="warning" text="dark" className="ms-1">
              Скрытый
            </Badge>
          )}
        </div>
      </Card.Header>
      <Card.Body>
        {/* Основная информация в компактном виде */}
        <Row className="mb-3 gh-2 d-flex">
          <Col xs="auto">
            <div className={`${styles.infoItem} mb-3`}>
              <i className="bi bi-geo-alt-fill text-info me-2" />
              Город: <strong>{item.city.name_ru}</strong>
            </div>
            {item.address && (
              <div className={`${styles.infoItem} mb-3`}>
                <i className="bi bi-pin-map-fill text-primary me-2" />
                Адрес: <strong>{item.address}</strong>
              </div>
            )}
          </Col>

          <Col xs="auto">
            <div className={`${styles.infoItem} mb-3`}>
              <i className="bi bi-calendar-event text-info me-2" />
              Дата старта: <strong>{getDate(item.start_time)}</strong>
            </div>
            <div className={`${styles.infoItem} mb-3`}>
              <i className="bi bi-123 text-info me-2" />
              Начало нумерации: <strong>{item.start_number}</strong>
            </div>
          </Col>

          <Col xs="auto">
            <div className={`${styles.infoItem} mb-3`}>
              <i className="bi bi-people-fill text-primary me-2" />
              Возрастные категории: <strong>{getAgeGroups()}</strong>
            </div>
            <div className={`${styles.infoItem} mb-3`}>
              <i className="bi bi-people-fill text-primary me-2" />
              Метод расчета возраста: <strong>{getAgeMethod()}</strong>
            </div>
          </Col>

          <Col xs="auto">
            <div className={`${styles.infoItem} mb-3`}>
              <i className="bi bi-shield-check text-info me-2" />
              Страховка:{' '}
              {hasInsurance ? (
                <strong className="text-success">Доступна ({item.insurance_cost} ₽)</strong>
              ) : (
                <strong className="text-secondary">Недоступна</strong>
              )}
            </div>
            <div className={`${styles.infoItem} mb-3`}>
              <i className="bi bi-cart2 text-info me-2" />
              Футболки:{' '}
              {item.shirt_unavailable ? (
                <strong className="text-danger">Недоступны</strong>
              ) : (
                <strong className="text-success">Доступны</strong>
              )}
            </div>
          </Col>
          <Col xs="auto">
            <div className={`${styles.infoItem} mb-3`}>
              <i className="bi bi-tag-fill text-info me-2" />
              ID:
              <Link to={`/city-format/${item.public_id}`}>
                <strong>{item.public_id}</strong>
              </Link>
              <OverlayTrigger placement="top" delay={{ show: 250, hide: 400 }} overlay={renderTooltip}>
                <Button variant="link" size="sm" className={styles.copyButton} onClick={copyToClipboard}>
                  <i className={`bi ${copySuccess ? 'bi-check-lg text-success' : 'bi-clipboard'}`} />
                </Button>
              </OverlayTrigger>
            </div>
          </Col>
        </Row>

        {/* Лимиты участников */}
        <Card className="mb-3 bg-body-tertiary">
          <Card.Header className={`${styles.innerCardHeader} py-2`}>
            <i className="bi bi-person-lines-fill me-2" />
            <strong>Лимиты</strong> <span className={styles.totalCount}>Всего: {getTotalLimit()}</span>
          </Card.Header>
          <Card.Body className="py-2">
            <Row className="g-2">
              <Col xs={12} sm={6} md={4} xxl={2}>
                <div className="d-flex align-items-center">
                  <i className="bi bi-person-fill text-primary me-2" />
                  <span>
                    Физики: <strong>{item.limits?.athlete || 0}</strong>
                  </span>
                </div>
              </Col>
              <Col xs={12} sm={6} md={4} xxl={2}>
                <div className="d-flex align-items-center">
                  <i className="bi bi-building text-info me-2" />
                  <span>
                    Корпы: <strong>{item.limits?.corporate || 0}</strong>
                  </span>
                </div>
              </Col>
              <Col xs={12} sm={6} md={4} xxl={2}>
                <div className="d-flex align-items-center">
                  <i className="bi bi-star-fill text-info me-2" />
                  <span>
                    Партнеры: <strong>{item.limits?.partner || 0}</strong>
                  </span>
                </div>
              </Col>
              <Col xs={12} sm={6} md={4} xxl={2}>
                <div className="d-flex align-items-center">
                  <i className="bi bi-people-fill text-info me-2" />
                  <span>
                    Другие: <strong>{item.limits?.other || 0}</strong>
                  </span>
                </div>
              </Col>
              <Col xs={12} sm={6} md={4} xxl={2}>
                <div className="d-flex align-items-center">
                  <i className="bi bi-award-fill text-info me-2" />
                  <span>
                    Бренд: <strong>{item.limits?.brand || 0}</strong>
                  </span>
                </div>
              </Col>
              <Col xs={12} sm={6} md={4} xxl={2}>
                <div className="d-flex align-items-center">
                  <i className="bi bi-trophy-fill text-info me-2" />
                  <span>
                    Спорт: <strong>{item.limits?.sport || 0}</strong>
                  </span>
                </div>
              </Col>
            </Row>
          </Card.Body>
        </Card>

        {/* Цены и график повышения */}
        {item.prices && item.prices.length > 0 && (
          <Card className={`${styles.innerCard} mb-3 bg-body-tertiary`}>
            <Card.Header className={`${styles.innerCardHeader} py-2`}>
              <i className="bi bi-cash-coin text-success me-2" />
              <strong>Цены и график повышения</strong>
            </Card.Header>
            <Card.Body className="py-2">
              <div className={styles.priceList}>
                {item.prices.map((priceItem, index) => (
                  <div key={index} className={styles.priceItem}>
                    <Badge bg="primary" className="me-2">
                      {priceItem.price} ₽
                    </Badge>
                    {priceItem.start_date ? (
                      <span>
                        <strong>{getDate(priceItem.start_date)}</strong>
                      </span>
                    ) : priceItem.tickets_count !== undefined ? (
                      <span>
                        <strong>{priceItem.tickets_count}</strong> билетов
                      </span>
                    ) : (
                      <span>Базовая цена</span>
                    )}
                  </div>
                ))}
              </div>
            </Card.Body>
          </Card>
        )}

        {/* Информационные блоки */}
        <div className={styles.infoBlocks}>
          <Row>
            {item.description && (
              <Col md={12} className="mb-3">
                <h6 className={styles.sectionTitle}>
                  <i className="bi bi-card-text text-info me-2" />
                  <strong>Описание</strong>
                </h6>
                <p className={styles.description}>{item.description}</p>
              </Col>
            )}
          </Row>
        </div>

        {/* Мерч, требования и заметки */}
        <Row>
          {item.merch && item.merch.length > 0 && (
            <Col md={6} className="mb-3">
              <h6 className={styles.sectionTitle}>
                <i className="bi bi-bag-check text-success me-2" />
                <strong>Мерч в комплекте</strong>
              </h6>
              <ul className={`${styles.listItems} list-group list-group-flush`}>
                {item.merch.map((merchItem, index) => (
                  <li key={index} className="bg-transparent px-0 py-1 d-flex align-items-center">
                    <i className="bi bi-check-circle-fill text-success me-2 small" />
                    {merchItem}
                  </li>
                ))}
              </ul>
            </Col>
          )}

          {item.requirements && item.requirements.length > 0 && (
            <Col md={6} className="mb-3">
              <h6 className={styles.sectionTitle}>
                <i className="bi bi-exclamation-triangle text-warning me-2" />
                <strong>Требования к участнику</strong>
              </h6>
              <ul className={`${styles.listItems} list-group list-group-flush`}>
                {item.requirements.map((requirement, index) => (
                  <li key={index} className="bg-transparent px-0 py-1 d-flex align-items-center">
                    <i className="bi bi-info-circle-fill text-primary me-2 small" />
                    {requirement}
                  </li>
                ))}
              </ul>
            </Col>
          )}

          {item.notes && item.notes.length > 0 && (
            <Col md={12} className="mb-3">
              <h6 className={styles.sectionTitle}>
                <i className="bi bi-journal-text text-primary me-2" />
                <strong>Заметки</strong>
              </h6>
              <ul className={`${styles.listItems} list-group list-group-flush`}>
                {item.notes.map((note, index) => (
                  <li key={index} className="bg-transparent px-0 py-1 d-flex align-items-center">
                    <i className="bi bi-sticky-fill text-warning me-2 small" />
                    {note}
                  </li>
                ))}
              </ul>
            </Col>
          )}
        </Row>
      </Card.Body>
    </Card>
  )
}
