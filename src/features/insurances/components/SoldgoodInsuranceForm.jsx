import { nanoid } from '@reduxjs/toolkit'
import useAxios from 'axios-hooks'
import moment from 'moment'
import { useEffect, useState } from 'react'
import { Col, FloatingLabel, Form, FormControl, Row } from 'react-bootstrap'
import { useForm } from 'react-hook-form'
import { Link } from 'react-router-dom'

import ImageField from '@/components/Forms/ImageField/ImageField'

import { APIRoute, AppRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import styles from '@/pages/BranchesScreen/components/BranchesForm/BranchesForm.module.scss'
import { removeEmptyString } from '@/utils/common'
import { unixToMoment } from '@/utils/date'
import { checkSetValue } from '@/utils/forms'

const fields = [
  {
    id: nanoid(),
    name: 'birth_date',
    label: 'Дата рождения',
    type: 'date',
    required: false,
  },
  {
    id: nanoid(),
    name: 'first_name',
    label: 'Имя (рус.)',
    type: 'text',
    required: false,
  },
  {
    id: nanoid(),
    name: 'last_name',
    label: 'Фамилия (рус.)',
    type: 'text',
    required: false,
  },
  {
    id: nanoid(),
    name: 'second_name',
    label: 'Отчество (рус.)',
    type: 'text',
    required: false,
  },
  {
    id: nanoid(),
    name: 'first_name_en',
    label: 'Имя (англ.)',
    type: 'text',
    required: false,
  },
  {
    id: nanoid(),
    name: 'last_name_en',
    label: 'Фамилия (англ.)',
    type: 'text',
    required: false,
  },
  {
    id: nanoid(),
    name: 'phone',
    label: 'Телефон (формат: 79998887766)',
    type: 'tel',
    pattern: /[7][0-9]{10}$/,
    required: false,
  },
  {
    id: nanoid(),
    name: 'email',
    label: 'Имейл',
    type: 'email',
    required: false,
  },
  {
    id: nanoid(),
    name: 'passport_serial',
    label: 'Серия паспорта',
    type: 'text',
    required: false,
  },
  {
    id: nanoid(),
    name: 'passport_number',
    label: 'Номер паспорта',
    type: 'text',
    required: false,
  },
  {
    id: nanoid(),
    name: 'city',
    label: 'Город проживания',
    type: 'text',
    required: false,
  },
  {
    id: nanoid(),
    name: 'passport_source',
    label: 'Орган, выдавший документ',
    type: 'text',
    required: false,
  },
  {
    id: nanoid(),
    name: 'passport_date',
    label: 'Дата выдачи документа',
    type: 'date',
    required: false,
  },
  {
    id: nanoid(),
    name: 'passport_birth_place',
    label: 'Место рождения',
    type: 'text',
    required: false,
  },
  {
    id: nanoid(),
    name: 'passport_code',
    label: 'Код подразделения',
    type: 'text',
    required: false,
  },
]

function SoldgoodInsuranceForm({ selectedItem, isClickRow, onUpdateLicences, setLicences, onCloseModal }) {
  const {
    register,
    setValue,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      first_name: selectedItem?.info?.first_name,
      last_name: selectedItem?.info?.last_name,
      second_name: selectedItem?.info?.second_name,
      first_name_en: selectedItem?.info?.first_name_en,
      last_name_en: selectedItem?.info?.last_name_en,
      birth_date: unixToMoment(selectedItem?.info?.birth_date).format('YYYY-MM-DD'),
      city: selectedItem?.info?.city,
      phone: selectedItem?.info?.phone,
      email: selectedItem?.info?.email,
      passport_number: selectedItem?.info?.passport_number,
      passport_serial: selectedItem?.info?.passport_serial,
      passport_source: selectedItem?.info?.passport_source,
      passport_date: unixToMoment(selectedItem?.info?.passport_date).format('YYYY-MM-DD'),
      passport_birth_place: selectedItem?.info?.passport_birth_place,
      passport_code: selectedItem?.info?.passport_code,
    },
  })

  const [startDateForm, setStartDateForm] = useState('')
  const [validityDateForm, setValidityDateForm] = useState('')

  useEffect(() => {
    if (startDateForm) {
      setValue('start_date', startDateForm)
    }

    if (validityDateForm) {
      setValue('validity_date', validityDateForm)
    }
  }, [startDateForm, validityDateForm, setValue])

  const openToast = useToast()

  const isEdit = Object.keys(selectedItem).length > 0

  const [, api] = useAxios(
    {
      url: APIRoute.UPDATE_INSURANCE,
      method: 'PUT',
    },
    { manual: true }
  )

  const handleSubmitForm = (data) => {
    const filteredData = removeEmptyString(data)

    if (Object.keys(filteredData).length > 0) {
      api({
        url: `${APIRoute.UPDATE_INSURANCE}/${selectedItem.public_id}`,
        data: filteredData,
      }).then((r) => {
        if (r.status === 200) {
          onUpdateLicences()
          setLicences([])
          onCloseModal()
          openToast.success({ message: 'Страховка успешно изменена' })
        }
      })
    }
  }

  const getPicturePath = (picture) => {
    if (typeof picture === 'string') {
      return picture
    }

    if (typeof picture === 'object') {
      return picture.path
    }
  }

  return (
    <Form onSubmit={handleSubmit(handleSubmitForm)} id="form">
      <Row className="g-3 mb-4">
        <Col md={12}>
          <Row>
            <Col md={isClickRow ? 'auto' : 12}>
              <ImageField
                fieldName="picture"
                isClickRow={isClickRow}
                imagePath={getPicturePath(selectedItem?.info.picture)}
                setValue={setValue}
              />
            </Col>

            {isClickRow && (
              <Col>
                {selectedItem?.info?.api_client?.policyNumber && (
                  <p>Полис: {selectedItem.info.api_client.policyNumber}</p>
                )}
                {selectedItem?.info?.api_client?.paidAt && (
                  <p>Дата: {moment(selectedItem.info.api_client.paidAt).format('DD MMMM YYYY, HH:mm:ss')}</p>
                )}
                {selectedItem?.order_public_id && (
                  <p>
                    Номер заказа:{' '}
                    <Link to={`${AppRoute.ORDERS}/${selectedItem?.order_public_id}`}>
                      {selectedItem?.order_public_id}
                    </Link>
                  </p>
                )}
                {selectedItem?.user_public_id && (
                  <p>
                    ID пользователя:{' '}
                    <Link to={`${AppRoute.USER_PROFILE}/${selectedItem?.user_public_id}`}>
                      {selectedItem?.user_public_id}
                    </Link>
                  </p>
                )}
              </Col>
            )}
          </Row>
        </Col>
        <Col md={6}>
          <Form.Group>
            <FloatingLabel controlId="startDateInput" label="Начало действия">
              <FormControl
                className={styles.field}
                type="datetime-local"
                defaultValue={unixToMoment(selectedItem?.info?.start_date).format('YYYY-MM-DDTHH:mm')}
                placeholder=""
                disabled={isClickRow}
                onChange={(e) => setStartDateForm(e.target.value)}
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        <Col md={6}>
          <Form.Group>
            <FloatingLabel controlId="validityDateInput" label="Окончание действия">
              <FormControl
                className={styles.field}
                type="datetime-local"
                defaultValue={unixToMoment(selectedItem?.info?.validity_date).format('YYYY-MM-DDTHH:mm')}
                placeholder=""
                disabled={isClickRow}
                onChange={(e) => setValidityDateForm(e.target.value)}
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        {fields.map((field) => (
          <Col md={6} key={field.id}>
            <Form.Group>
              <FloatingLabel controlId={`${field.name}Input`} label={field.label}>
                <FormControl
                  className={styles.field}
                  {...register(field.name, {
                    required: field.required && !isEdit,
                    pattern: field.pattern,
                    setValueAs: (v) => checkSetValue(v, selectedItem?.info?.[field.name], field.type),
                  })}
                  type={field.type}
                  isInvalid={errors[field.name]}
                  placeholder={field.label}
                  disabled={isClickRow}
                />
              </FloatingLabel>
            </Form.Group>
          </Col>
        ))}
      </Row>
    </Form>
  )
}

export default SoldgoodInsuranceForm
