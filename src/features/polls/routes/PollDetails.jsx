import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Col, Container, ListGroup, ProgressBar, Row, Spinner } from 'react-bootstrap'
import { useParams, Link } from 'react-router-dom'

import Layout from '@/components/Layout/Layout'

import { useGetPoll } from '@/features/polls/api/getPoll'
import { times } from '@/utils/common'

export const PollDetails = () => {
  const { public_id } = useParams()
  const { data, isLoading, error } = useGetPoll(public_id)
  const poll = data?.data
  const totalVotes = poll?.options?.reduce((sum, o) => sum + (typeof o.votes === 'number' ? o.votes : 0), 0) || 0

  return (
    <Layout>
      <Button variant="outline-secondary mb-3" size="sm" onClick={() => history.back()}>
        ← Назад к списку
      </Button>

      <h2 className="mb-4">Голосование</h2>
      {isLoading ? (
        <Container className="d-flex justify-content-center align-items-center" style={{ height: '50vh' }}>
          <Spinner animation="border" />
        </Container>
      ) : error ? (
        <Container className="d-flex justify-content-center align-items-center" style={{ height: '50vh' }}>
          <p>Не удалось загрузить данные голосования</p>
        </Container>
      ) : poll ? (
        <Card>
          <Card.Header className="bg-body-secondary">
            <div className="d-flex align-items-center justify-content-between">
              <div className="d-flex align-items-center gap-2">
                <Card.Title className="mb-0">{poll.question}</Card.Title>
              </div>
              <div>
                <Badge bg={poll.public_results ? 'success' : 'secondary'}>
                  {poll.public_results ? 'Публичный' : 'Скрытый'}
                </Badge>
              </div>
            </div>
          </Card.Header>
          <Card.Body>
            <Row className="mb-3">
              <Col>
                <div>
                  <strong>Создано:</strong> {poll.created_date ? times.getFullDate(poll.created_date) : '-'}
                </div>
                <div>
                  <strong>Истекает:</strong> {poll.expires_at ? times.getFullDate(poll.expires_at) : '-'}
                </div>
                <div>
                  <strong>Пользователь:</strong> <Link to={`/user/${poll.user_public_id}`}>{poll.user_public_id}</Link>
                </div>
              </Col>
            </Row>
            <Row>
              <Col md={8} lg={6}>
                <h5>Варианты ответа</h5>
                <ListGroup>
                  {poll.options?.map((opt) => {
                    const votes = typeof opt.votes === 'number' ? opt.votes : 0
                    const percent = totalVotes > 0 ? Math.round((votes * 100) / totalVotes) : 0
                    return (
                      <ListGroup.Item key={opt.id}>
                        <div className="d-flex justify-content-between align-items-center mb-1">
                          <span>{opt.text}</span>
                          <span className="ms-2">
                            <Badge bg="primary" className="me-2">
                              {votes}
                            </Badge>
                            <span>{percent}%</span>
                          </span>
                        </div>
                        <ProgressBar now={percent} variant="info" style={{ height: '8px' }} />
                      </ListGroup.Item>
                    )
                  })}
                </ListGroup>
              </Col>
            </Row>
          </Card.Body>
        </Card>
      ) : null}
    </Layout>
  )
}
