import Quill from 'quill'
import { useLayoutEffect, useEffect, useRef } from 'react'
import 'quill/dist/quill.snow.css'

const QuillEditor = ({ value, onChange, placeholder, theme = 'snow', readOnly = false }) => {
  const containerRef = useRef(null)
  const quillRef = useRef(null)
  const valueRef = useRef(value)
  const onChangeRef = useRef(onChange)
  const isSilent = useRef(false)

  useLayoutEffect(() => {
    onChangeRef.current = onChange
  })

  useEffect(() => {
    if (quillRef.current) {
      const editorHTML = quillRef.current.root.innerHTML
      const valueHTML = value || ''

      if (valueHTML !== editorHTML) {
        isSilent.current = true
        quillRef.current.clipboard.dangerouslyPasteHTML(valueHTML)
      }
    }
  }, [value])

  useEffect(() => {
    const container = containerRef.current
    const editorContainer = container.appendChild(container.ownerDocument.createElement('div'))
    const quill = new Quill(editorContainer, {
      theme,
      readOnly,
      placeholder,
      modules: {
        toolbar: [
          [{ header: [1, 2, 3, 4, 5, 6, false] }],
          ['bold', 'italic', 'underline', 'strike'],
          [{ list: 'ordered' }, { list: 'bullet' }],
          [{ align: [] }],
          ['link', 'image'],
          ['clean'],
        ],
      },
    })

    quillRef.current = quill

    if (valueRef.current) {
      isSilent.current = true
      quill.clipboard.dangerouslyPasteHTML(valueRef.current)
    }

    quill.on(Quill.events.TEXT_CHANGE, () => {
      if (!isSilent.current) {
        onChangeRef.current?.(quill.root.innerHTML)
      }
      isSilent.current = false
    })

    return () => {
      quillRef.current = null
      container.innerHTML = ''
    }
  }, [theme, readOnly, placeholder])

  // Sync value prop to editor
  useEffect(() => {
    const quill = quillRef.current
    if (quill && !quill.hasFocus()) {
      const editorHTML = quill.root.innerHTML
      const valueHTML = value || ''

      if (valueHTML === '' && editorHTML === '<p><br></p>') {
        return
      }

      if (valueHTML !== editorHTML) {
        const delta = quill.clipboard.convert(valueHTML)
        quill.setContents(delta, 'silent')
      }
    }
  }, [value])

  return <div ref={containerRef} />
}

export default QuillEditor
