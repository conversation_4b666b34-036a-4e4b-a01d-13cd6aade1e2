import { Parser } from 'html-to-react'
import moment from 'moment'
import { But<PERSON>, Col, Figure, FloatingLabel, Form, FormControl, FormGroup, Modal, Row } from 'react-bootstrap'

import QuillEditor from '@/components/QuillEditor/QuillEditor'

import { MAX_IMG_SIZE } from '@/const'
import { useGetCountryRegions } from '@/features/cities/api/getCountryRegions'
import { useToast } from '@/hooks/useToast'
import { convertBase64 } from '@/utils/common'
import { updateFormData } from '@/utils/forms'
import { getImageSrc } from '@/utils/images'

import styles from '../Participants/Participants.module.scss'

function AthletesForm({
  isClickRow,
  sendData,
  formData,
  defaultFormData,
  isNewUser,
  handleChangeForm,
  group,
  editUserPopup,
  handleClosePopup,
  isChangedData,
}) {
  const openToast = useToast()
  const { data: regionsData } = useGetCountryRegions('RU')
  const regions = regionsData?.data?.values || []

  let title = isNewUser ? 'Добавить пользователя' : 'Изменение данных'
  title = isClickRow ? 'Спортсмен' : title

  const handleChangeField = (evt) => {
    updateFormData(evt, formData, handleChangeForm, defaultFormData)
  }

  const handleEditorChange = (html) => {
    handleChangeForm((prev) => ({ ...prev, description: html }))
  }

  const handleFileRead = async (evt) => {
    const file = evt.target.files[0]
    const fileSizeInB = file.size
    const base64 = await convertBase64(file)

    const newNewsStoryData = { ...formData }

    if (base64 === '') {
      newNewsStoryData.picture = ''
      handleChangeForm({ ...newNewsStoryData })
    } else if (fileSizeInB <= MAX_IMG_SIZE) {
      handleChangeForm({ ...formData, picture: base64 })
    } else if (fileSizeInB > MAX_IMG_SIZE) {
      openToast.error({
        title: true,
        message: `Файл слишком большой: ${file.name}`,
        duration: 6000,
      })
      evt.target.value = ''
    }
  }

  const handleDeletePictures = (evt) => {
    const copyNewsStoryData = { ...formData }

    delete copyNewsStoryData.picture

    handleChangeForm({ ...copyNewsStoryData })
    evt.target.value = ''
  }

  return (
    <Modal show={editUserPopup} onHide={handleClosePopup}>
      <Modal.Header closeButton>
        <Modal.Title>{title}</Modal.Title>
      </Modal.Header>
      <Form onSubmit={sendData}>
        <Modal.Body>
          <FormGroup controlId="nameForm">
            <Row className="mb-3">
              {(formData.picture || defaultFormData.picture) && (
                <Col className="d-grid justify-content-center mb-2" md={!isClickRow ? 12 : 'auto'}>
                  <Figure className="m-0">
                    <Figure.Image
                      className={styles.image}
                      width={95}
                      alt="171x180"
                      src={getImageSrc(formData.picture || defaultFormData.picture)}
                    />
                  </Figure>
                  {!isNewUser && formData.picture && (
                    <Button onClick={handleDeletePictures} variant="link" size="sm">
                      удалить
                    </Button>
                  )}
                </Col>
              )}

              {!isClickRow && (
                <Col className="mb-1">
                  <Form.Group controlId="formPictureFile">
                    <Form.Label
                      className={`${styles.fileButton} ${
                        formData.picture || defaultFormData.picture ? styles.addedImage : ''
                      }`}
                    >
                      {formData.picture || defaultFormData.picture ? 'Заменить фотографию' : 'Выберите фотографию'}
                    </Form.Label>
                    <Form.Control
                      className="visually-hidden"
                      type="file"
                      onChange={handleFileRead}
                      name="picture"
                      accept=".jpg, .png, .jpeg"
                    />
                  </Form.Group>
                </Col>
              )}
            </Row>
          </FormGroup>

          <Row className="g-3 mb-3">
            <Col md={12}>
              <FloatingLabel controlId="nameLabel" label="ФИО">
                <FormControl
                  onChange={handleChangeField}
                  name="name"
                  type="text"
                  defaultValue={defaultFormData?.name}
                  placeholder="Фио"
                  disabled={isClickRow}
                  required
                />
              </FloatingLabel>
            </Col>

            <Col md={6}>
              <FloatingLabel controlId="titleLabel" label="Спортивное звание">
                <FormControl
                  onChange={handleChangeField}
                  name="title"
                  type="text"
                  defaultValue={defaultFormData?.title}
                  placeholder="Спортивное звание"
                  disabled={isClickRow}
                  required
                />
              </FloatingLabel>
            </Col>

            <Col md={6}>
              <FloatingLabel controlId="birthdayLabel" label="Дата рождения">
                <FormControl
                  onChange={handleChangeField}
                  name="birthday"
                  type="date"
                  defaultValue={moment(defaultFormData.birthday).format('YYYY-MM-DD')}
                  placeholder="Дата рождения"
                  disabled={isClickRow}
                  required
                />
              </FloatingLabel>
            </Col>

            <Col md={12}>
              <FloatingLabel controlId="regionIdLabel" label="Регион">
                <FormControl
                  onChange={handleChangeField}
                  name="region_id"
                  as="select"
                  defaultValue={defaultFormData.region_id}
                  placeholder="Регион"
                  disabled={isClickRow}
                  required
                >
                  <option>Выберите регион</option>
                  {regions.map((el) => (
                    <option key={el.id} value={el.id}>
                      {el.name_ru}
                    </option>
                  ))}
                </FormControl>
              </FloatingLabel>
            </Col>

            <Col md={12}>
              <FloatingLabel controlId="companyLabel" label="Организация">
                <FormControl
                  onChange={handleChangeField}
                  name="company"
                  type="text"
                  defaultValue={defaultFormData.company}
                  placeholder="Организация"
                  disabled={isClickRow}
                  required
                />
              </FloatingLabel>
            </Col>

            <Col md={12}>
              <FloatingLabel controlId="agentNameLabel" label="Тренер в настоящий момент">
                <FormControl
                  onChange={handleChangeField}
                  name="agent_name"
                  type="text"
                  defaultValue={defaultFormData.agent_name}
                  placeholder="Тренер в настоящий момент"
                  disabled={isClickRow}
                  required
                />
              </FloatingLabel>
            </Col>

            <Col md={12}>
              <Row>
                <Col>
                  <FloatingLabel controlId="groupPublicIdLabel" label="Сборная">
                    <FormControl
                      onChange={handleChangeField}
                      name="group_public_id"
                      as="select"
                      defaultValue={defaultFormData.group_public_id}
                      placeholder="Сборная"
                      disabled={isClickRow}
                      required
                    >
                      <option>Выберите сборную</option>
                      {group.map((el) => (
                        <option key={el.public_id} value={el.public_id}>
                          {el.name}
                        </option>
                      ))}
                    </FormControl>
                  </FloatingLabel>
                </Col>
                <Col>
                  <FloatingLabel controlId="yearLabel" label="Год">
                    <FormControl
                      onChange={handleChangeField}
                      name="year"
                      type="number"
                      defaultValue={defaultFormData.year}
                      placeholder="Год"
                      disabled={isClickRow}
                    />
                  </FloatingLabel>
                </Col>
              </Row>
            </Col>
          </Row>

          {isClickRow ? (
            <Row className={styles.descWrapper}>
              <Col xs={12}>
                <span className={styles.descLabel}>Описание</span>
              </Col>
              <Col>
                <p className="mt-2 text-primary">Достижения:</p>
                {Parser().parse(defaultFormData?.description)}
              </Col>
            </Row>
          ) : (
            <FormGroup className={styles.editorWrapper}>
              {!isClickRow && (
                <QuillEditor
                  value={formData.description || defaultFormData.description || ''}
                  onChange={handleEditorChange}
                  placeholder="Достижения..."
                />
              )}
            </FormGroup>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleClosePopup}>
            Закрыть
          </Button>
          {!isClickRow && (
            <Button type="submit" variant="primary" disabled={!isChangedData}>
              Сохранить
            </Button>
          )}
        </Modal.Footer>
      </Form>
    </Modal>
  )
}

export default AthletesForm
