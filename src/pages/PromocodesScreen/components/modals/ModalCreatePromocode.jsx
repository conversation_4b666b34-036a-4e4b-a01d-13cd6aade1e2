import useAxios from 'axios-hooks'
import moment from 'moment-timezone'
import { useEffect, useRef, useState, useCallback, useMemo } from 'react'
import { Button, Col, Form, FormControl, FormGroup, Modal, Row } from 'react-bootstrap'

import { APIRoute, Kind, Role } from '@/const'
import { useGetInsuranceTypes } from '@/features/insurances/api/getInsuranceTypes'
import { useGetPromoGoods, useGetPromoCode } from '@/features/promocodes/api'
import { Tag } from '@/pages/ShopScreen/components/Delivery/Delivery'
import { timezones } from '@/timezones/timezones'
import { getDateToday, getKindName } from '@/utils/common'
import storage from '@/utils/storage'

// Constants
const PROMOCODE_TYPES = [
  { value: 'ticket', label: 'Билет' },
  { value: 'product', label: 'Товар' },
  { value: 'insurance', label: 'Страховка' },
]

const DEFAULT_TIMEZONE = 'Europe/Moscow'

// Utility functions
const dateWithOffset = (date, timezone) => {
  return moment.tz(date, timezone).utc().toISOString()
}

const formatDateForInput = (date) => {
  return moment(date).format('YYYY-MM-DDTHH:mm')
}

// Функция для фильтрации событий и городов по правам админа
const filterEventsByAdminRights = (events, userId) => {
  if (!events || !userId) return []

  return events
    .map((event) => {
      if (!event.event_city) return event

      // Фильтруем города, где пользователь является админом
      const filteredCities = event.event_city.filter((city) => {
        return city.roles?.city_admin?.includes(userId)
      })

      // Возвращаем событие только если есть доступные города
      if (filteredCities.length > 0) {
        return {
          ...event,
          event_city: filteredCities,
        }
      }

      return null
    })
    .filter(Boolean) // Убираем null значения
}

// ItemSelectionModal Component
const ItemSelectionModal = ({ show, onHide, items, itemType, onChange, onSave }) => {
  const isProduct = itemType === 'product'
  const title = isProduct ? 'товар' : 'страховку'
  const titlePlural = isProduct ? 'товары' : 'страховки'
  const labelField = isProduct ? 'title' : 'name'

  return (
    <Modal show={show} onHide={onHide}>
      <Modal.Header closeButton>Добавить {title}</Modal.Header>
      <Modal.Body>
        {items?.length > 0 && (
          <FormGroup controlId={`${itemType}Form`}>
            <Row style={{ marginBottom: '40px' }}>
              <Col xs={4}>
                <Form.Label>{isProduct ? 'Товар' : 'Страховка'}</Form.Label>
              </Col>
              <Col>
                <FormControl
                  as="select"
                  aria-label={itemType}
                  name={itemType}
                  onChange={(e) => onChange(e, itemType)}
                  className="mb-3"
                  multiple
                  required
                >
                  <option>Выберите {title}</option>
                  <option style={{ marginBottom: '10px' }} value={'all'}>
                    Все {titlePlural}
                  </option>
                  {items.map((item) => (
                    <option value={item.public_id} key={item.public_id}>
                      {item[labelField]}
                    </option>
                  ))}
                </FormControl>
              </Col>
            </Row>
          </FormGroup>
        )}
      </Modal.Body>
      <Modal.Footer>
        <Button variant="link" onClick={onHide}>
          Отменить
        </Button>
        <Button onClick={() => onSave(itemType)}>Сохранить</Button>
      </Modal.Footer>
    </Modal>
  )
}

// Main Component
function ModalCreatePromocode({
  show,
  handleCloseModal,
  newPromocodes,
  handleResultModal,
  getPromocodes,
  editPromocode,
  isEditPromocode,
  isClonePromocode = false,
}) {
  // Получаем данные пользователя
  const userStorage = storage.getUserObj()
  const userId = userStorage?.public_id
  const hasPromoRole = userStorage?.role?.includes(Role.PROMO) || userStorage?.role?.includes(Role.SUPERADMIN)

  // State declarations
  const [selectedEvent, setSelectedEvent] = useState(null)
  const [selectedFormats, setSelectedFormats] = useState([])
  const [formats, setFormats] = useState([])
  const [isOneGenerateCode, setIsOneGenerateCode] = useState(true)
  const [endDateForm, setEndDateForm] = useState('')
  const [timezone, setTimezone] = useState(DEFAULT_TIMEZONE)
  const [startDateForm, setStartDateForm] = useState(`${getDateToday()}`)
  const [filteredEventsData, setFilteredEventsData] = useState([])
  const [promocodeForm, setPromocodeForm] = useState({})
  const [selectedPromoType, setSelectedPromoType] = useState('ticket')
  const [productModal, setProductModal] = useState(false)
  const [insuranceModal, setInsuranceModal] = useState(false)
  const [selectedProducts, setSelectedProducts] = useState([])
  const [selectedInsurances, setSelectedInsurances] = useState([])
  const [isFormatsError, setIsFormatsError] = useState(false)

  // Hooks and API calls
  const { data: goods = [] } = useGetPromoGoods()
  const { data: onePromoCode } = useGetPromoCode(editPromocode.code)
  const getInsuranceTypesQuery = useGetInsuranceTypes()
  const insurances = getInsuranceTypesQuery?.data?.data?.values

  const selectFormatsRef = useRef(null)

  const [, api] = useAxios(
    {
      method: 'GET',
    },
    { manual: true }
  )

  // Memoized values
  const defaultFormValues = useMemo(
    () => ({
      kind: Object.values(Kind)[0],
      active: true,
      discount: 10,
      discount_form: 'percent',
      cities: [],
    }),
    []
  )

  // Callback functions
  const loadAndRestoreFormats = useCallback(
    async (event, formatIds, eventCityIds) => {
      if (!event || !event.event_city || !formatIds || formatIds.length === 0) return

      try {
        let allFormats = []
        const selectedFormats = []
        const cityIds = new Set()

        // Load formats for all promocode cities
        for (const city of eventCityIds) {
          const result = await api({
            url: `${APIRoute.EVENT_FORMATS}/${city}?hidden=true`,
          })

          if (result.data.values.length > 0) {
            // Filter formats, keeping only those in the original promocode
            const matchingFormats = result.data.values.filter((format) => formatIds.includes(format.public_id))

            allFormats = [...result.data.values]
            selectedFormats.push(...matchingFormats)
            matchingFormats.forEach((format) => {
              if (format.city && format.city.id) {
                cityIds.add(format.city.id)
              }
            })
          }
        }

        if (allFormats.length > 0) {
          setSelectedFormats(selectedFormats)
          setFormats(allFormats)

          setPromocodeForm((prev) => ({
            ...prev,
            formats: selectedFormats.map((f) => f.public_id),
            cities: [...cityIds],
          }))
        }
      } catch (error) {
        console.error('Error loading formats:', error)
      }
    },
    [api]
  )

  const handleClosePopup = useCallback(() => {
    handleCloseModal(false)
    setTimezone(DEFAULT_TIMEZONE)
    setStartDateForm(`${getDateToday()}`)
    setEndDateForm('')
    setSelectedEvent(null)
    setFormats([])
    setPromocodeForm({})
    setIsOneGenerateCode(true)
    setSelectedPromoType('ticket')
    setSelectedProducts([])
    setSelectedFormats([])
    setSelectedInsurances([])
    setIsFormatsError(false)
  }, [handleCloseModal])

  const updateFormField = useCallback((updates) => {
    setPromocodeForm((prev) => ({ ...prev, ...updates }))
  }, [])

  const updateDatesWithTimezone = useCallback(
    (selectedTimezone) => {
      const startDateWithOffset = startDateForm ? dateWithOffset(startDateForm, selectedTimezone) : null
      const endDateWithOffset = endDateForm ? dateWithOffset(endDateForm, selectedTimezone) : null

      updateFormField({
        start_date: startDateWithOffset,
        end_date: endDateWithOffset,
      })
    },
    [startDateForm, endDateForm, updateFormField]
  )

  // Event handlers
  const handleChangeTypes = useCallback(
    (e) => {
      const newType = e.target.value
      setSelectedPromoType(newType)
      updateFormField({ soldgood_type: newType })
      setSelectedProducts([])
      setIsFormatsError(false)
    },
    [updateFormField]
  )

  const handleItemChange = useCallback(
    (evt, itemType) => {
      const isProduct = itemType === 'product'
      const items = isProduct ? goods : insurances
      const stateKey = isProduct ? 'products' : 'insurances'

      if (evt.target.value === 'all') {
        const itemIds = items?.map((el) => el.public_id) || []

        if (isProduct) {
          setSelectedProducts(items)
        } else {
          setSelectedInsurances(items)
        }

        updateFormField({ [stateKey]: itemIds })
      } else {
        const selectedOptions = Array.from(evt.target.selectedOptions)
        const itemIds = selectedOptions.map((option) => option.value)
        updateFormField({ [stateKey]: itemIds })
      }
    },
    [goods, insurances, updateFormField]
  )

  const handleSaveItems = useCallback(
    (itemType) => {
      const isProduct = itemType === 'product'
      const items = isProduct ? goods : insurances
      const stateKey = isProduct ? 'products' : 'insurances'
      const labelField = isProduct ? 'title' : 'name'

      const selectedItems =
        items
          ?.filter((item) => promocodeForm[stateKey]?.includes(item.public_id))
          .map((item) => ({ label: item[labelField], public_id: item.public_id })) || []

      if (isProduct) {
        setSelectedProducts(selectedItems)
        setProductModal(false)
      } else {
        setSelectedInsurances(selectedItems)
        setInsuranceModal(false)
      }
    },
    [goods, insurances, promocodeForm]
  )

  const handleDeleteCurrentItem = useCallback(
    (item, itemType) => {
      const isProduct = itemType === 'product'
      const stateKey = isProduct ? 'products' : 'insurances'
      const items = isProduct ? selectedProducts : selectedInsurances

      const filteredItems = items.filter((el) => el.public_id !== item.public_id)
      const newItemIds = filteredItems.map((el) => el.public_id)

      if (isProduct) {
        setSelectedProducts(filteredItems)
      } else {
        setSelectedInsurances(filteredItems)
      }

      updateFormField({ [stateKey]: newItemIds })
    },
    [selectedProducts, selectedInsurances, updateFormField]
  )

  const handleEvent = useCallback(
    (e) => {
      const event = filteredEventsData.find((event) => event.public_id === e.target.value)
      setSelectedEvent(event)
      setFormats([])
    },
    [filteredEventsData]
  )

  const handleLimit = useCallback(
    (e) => {
      const limit = Object.values(Kind).find((el) => el === e.target.value)
      if (limit) {
        updateFormField({ kind: limit })
      }
    },
    [updateFormField]
  )

  const handleChangeCity = useCallback(
    async (evt) => {
      const cityPublicId = evt.target.value

      if (cityPublicId === 'all') {
        const formats = []
        for (const city of selectedEvent.event_city) {
          try {
            const result = await api({
              url: `${APIRoute.EVENT_FORMATS}/${city.public_id}?hidden=true`,
            })
            if (result.data.values.length > 0) {
              formats.push(...result.data.values)
            }
          } catch (e) {
            console.error(e)
          }
        }
        setFormats(formats)
      } else {
        const findCity = selectedEvent.event_city.find((item) => item.public_id === cityPublicId)
        if (findCity) {
          try {
            const result = await api({
              url: `${APIRoute.EVENT_FORMATS}/${findCity.public_id}?hidden=true`,
            })
            setFormats(result.data.values || [])
          } catch (e) {
            console.error(e)
          }
        }
      }
    },
    [selectedEvent, api]
  )

  const handleSelectFormats = useCallback(() => {
    const selectedOptions = Array.from(selectFormatsRef.current.selectedOptions)
    const newFormats = selectedOptions
      .map((option) => formats.find((item) => item.public_id === option.value))
      .filter((format) => format && !selectedFormats.some((item) => item.public_id === format.public_id))

    const updatedFormats = [...selectedFormats, ...newFormats]
    const idFormats = updatedFormats.map((item) => item.public_id)
    const idCities = [...new Set(updatedFormats.map((item) => item.city.id))]

    setSelectedFormats(updatedFormats)
    updateFormField({
      cities: idCities,
      formats: idFormats,
    })
    setIsFormatsError(false)
  }, [formats, selectedFormats, updateFormField])

  const onSelectTimezone = useCallback(
    (evt) => {
      const selectedTimezone = evt.target.value
      setTimezone(selectedTimezone)
      updateDatesWithTimezone(selectedTimezone)
    },
    [updateDatesWithTimezone]
  )

  const handleChangeDate = useCallback(
    (evt) => {
      const name = evt.target.name
      const newDate = evt.target.value
      const newDateWithOffset = dateWithOffset(newDate, timezone)

      if (name === 'start_date') {
        setStartDateForm(newDate)
      } else if (name === 'end_date') {
        setEndDateForm(newDate)
      }

      updateFormField({ [name]: newDateWithOffset })
    },
    [timezone, updateFormField]
  )

  const handlePromocodeChange = useCallback(
    (e) => {
      const cleanCode = e.target.value.trim().replace(/\s+/g, '')
      updateFormField({ code: cleanCode })
    },
    [updateFormField]
  )

  const handleDelSelectedFormat = useCallback(
    (format) => {
      const filteredFormats = selectedFormats.filter((item) => item.public_id !== format.public_id)
      const idFormats = filteredFormats.map((format) => format.public_id)
      const idCities = [...new Set(filteredFormats.map((format) => format.city.id))]

      setSelectedFormats(filteredFormats)
      updateFormField({
        cities: idCities,
        formats: idFormats,
      })
    },
    [selectedFormats, updateFormField]
  )

  const sendData = useCallback(
    async (e) => {
      e.preventDefault()

      const body = { ...promocodeForm }
      let method = 'POST'

      // Clean up body based on promo type
      if (selectedPromoType === 'product') {
        delete body.formats
        delete body.insurances
      } else if (selectedPromoType === 'ticket') {
        delete body.products
        delete body.insurances
      } else if (selectedPromoType === 'insurance') {
        delete body.formats
        delete body.products
      }

      // Determine API path and method
      let path
      if (isEditPromocode) {
        method = 'PUT'
        path = APIRoute.PROMOCODE
        // Remove fields that are not allowed in edit mode
        delete body.cities
        delete body.formats
      } else {
        if (isOneGenerateCode) {
          delete body.count
          path = APIRoute.GENERATE_PROMOCODE
        } else {
          delete body.code
          path = APIRoute.GENERATE_PROMOCODES
        }
      }

      // Validate data before sending
      if (Object.keys(editPromocode).length === 0 && selectedPromoType === 'ticket' && !body?.formats?.length) {
        setIsFormatsError(true)
        return
      }

      try {
        const result = await api({ url: path, method, data: body })
        if (result.status === 200) {
          handleClosePopup()
          getPromocodes()

          if (!isOneGenerateCode) {
            newPromocodes(result.data)
            handleResultModal(true)
          }
        }
      } catch (error) {
        console.error('Error saving promocode:', error)
      }
    },
    [
      promocodeForm,
      selectedPromoType,
      isEditPromocode,
      isOneGenerateCode,
      editPromocode,
      api,
      handleClosePopup,
      getPromocodes,
      newPromocodes,
      handleResultModal,
    ]
  )

  // Effects
  useEffect(() => {
    updateFormField(defaultFormValues)
  }, [defaultFormValues, updateFormField])

  // Initialize dates when modal opens for editing
  useEffect(() => {
    if (show && editPromocode && isEditPromocode) {
      setStartDateForm(formatDateForInput(editPromocode.start_date))
      setEndDateForm(formatDateForInput(editPromocode.end_date))
    }
  }, [show, editPromocode, isEditPromocode])

  // Set promo type when onePromoCode loads
  useEffect(() => {
    if (onePromoCode?.soldgood_type) {
      setSelectedPromoType(onePromoCode.soldgood_type)
    } else if (onePromoCode) {
      setSelectedPromoType('ticket')
    }
  }, [onePromoCode])

  // Initialize form data for cloning mode
  useEffect(() => {
    if (onePromoCode && !isEditPromocode && isClonePromocode) {
      const start_date = formatDateForInput(onePromoCode.start_date) || startDateForm
      const end_date = formatDateForInput(onePromoCode.end_date) || ''

      setStartDateForm(start_date)
      setEndDateForm(end_date)

      updateFormField({
        code: '',
        description: onePromoCode.description || '',
        discount: onePromoCode.discount || 10,
        discount_form: onePromoCode.discount_form || 'percent',
        available_count: onePromoCode.available_count || 0,
        start_date: start_date,
        end_date: end_date,
        active: onePromoCode.active !== undefined ? onePromoCode.active : true,
        kind: onePromoCode.kind || Object.values(Kind)[0],
        products: onePromoCode.products || [],
        insurances: onePromoCode.insurances || [],
        formats: onePromoCode.formats || [],
        cities: onePromoCode.cities || [],
        soldgood_type: onePromoCode.soldgood_type || 'ticket',
      })
    }
  }, [onePromoCode, isEditPromocode, isClonePromocode, startDateForm, updateFormField])

  // Set selected products for cloning
  useEffect(() => {
    if (onePromoCode?.products && goods && isClonePromocode && !isEditPromocode) {
      const selected_products = goods
        .filter((el) => onePromoCode.products.includes(el.public_id))
        .map((el) => ({ label: el.title, public_id: el.public_id }))

      setSelectedProducts(selected_products)
    }
  }, [onePromoCode?.products, goods, isClonePromocode, isEditPromocode])

  // Set selected insurances for cloning
  useEffect(() => {
    if (onePromoCode?.insurances && insurances && isClonePromocode && !isEditPromocode) {
      const selected_insurances = insurances
        .filter((el) => onePromoCode.insurances.includes(el.public_id))
        .map((el) => ({ label: el.name, public_id: el.public_id }))

      setSelectedInsurances(selected_insurances)
    }
  }, [onePromoCode?.insurances, insurances, isClonePromocode, isEditPromocode])

  // Load events list only when modal opens
  useEffect(() => {
    if (show) {
      api({ url: APIRoute.EVENT_LIST }).then((r) => {
        if (r.status === 200) {
          const eventsData = r.data.values

          // Фильтруем события по правам админа только если у пользователя нет роли promo
          if (hasPromoRole) {
            // Если есть роль promo - показываем все события
            setFilteredEventsData(eventsData)
          } else {
            // Если нет роли promo - фильтруем по правам админа
            const filtered = filterEventsByAdminRights(eventsData, userId)
            setFilteredEventsData(filtered)
          }
        }
      })
    }
  }, [show, api, userId, hasPromoRole])

  // Handle event selection and form initialization when onePromoCode or eventsData changes
  useEffect(() => {
    if (show && filteredEventsData.length > 0 && onePromoCode && Object.keys(onePromoCode).length > 0) {
      const findEvent = filteredEventsData.find((item) => item.public_id === onePromoCode.event_ids?.[0])

      if (findEvent) {
        setSelectedEvent({ ...findEvent })

        // Set code for non-clone mode
        if (!isClonePromocode) {
          updateFormField({ code: editPromocode.code || '' })
        }

        // Load and restore formats for clone mode
        if (isClonePromocode && onePromoCode?.formats?.length > 0) {
          loadAndRestoreFormats(findEvent, onePromoCode.formats, onePromoCode.event_city_ids)
        }
      }
    }
  }, [
    show,
    filteredEventsData,
    onePromoCode,
    isClonePromocode,
    editPromocode.code,
    loadAndRestoreFormats,
    updateFormField,
  ])

  // Initialize form with default values when modal opens for new promocode
  useEffect(() => {
    if (show && Object.keys(editPromocode).length === 0 && !isClonePromocode) {
      const startDate = dateWithOffset(startDateForm, timezone)
      updateFormField({
        start_date: startDate,
        ...defaultFormValues,
      })
    }
  }, [show, editPromocode, isClonePromocode, startDateForm, timezone, defaultFormValues, updateFormField])

  // Render helper functions
  const renderSelectedItems = useCallback(
    (items, itemType) => {
      const isProduct = itemType === 'product'
      const title = isProduct ? 'товары' : 'страховки'

      return (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
          <span>
            Выбранные {title}:{' '}
            {items.length > 0 &&
              items.map((item) => (
                <Tag onClick={() => handleDeleteCurrentItem(item, itemType)} key={item?.public_id}>
                  {item?.label}
                </Tag>
              ))}
          </span>
        </div>
      )
    },
    [handleDeleteCurrentItem]
  )

  return (
    <>
      <Modal show={show} onHide={handleClosePopup}>
        <Form onSubmit={sendData} style={{ borderRadius: '4px' }}>
          <Modal.Header closeButton>
            <Modal.Title>Создание промокода</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form.Check
              className="mb-4"
              type="switch"
              id="custom-switch"
              label="Создание множества промокодов"
              onChange={() => setIsOneGenerateCode(!isOneGenerateCode)}
            />

            {isOneGenerateCode && (
              <FormGroup controlId="promocodeNameForm">
                <Row>
                  <Col xs={4}>
                    <Form.Label>Название промокода</Form.Label>
                  </Col>
                  <Col>
                    <FormControl
                      type="text"
                      name="promocode"
                      placeholder="CODE25"
                      onChange={handlePromocodeChange}
                      className="mb-3"
                      value={promocodeForm.code || editPromocode.code || ''}
                      minLength={5}
                      disabled={isEditPromocode}
                      required
                    />
                  </Col>
                </Row>
              </FormGroup>
            )}

            {!isOneGenerateCode && (
              <FormGroup controlId="countForm">
                <Row>
                  <Col xs={4}>
                    <Form.Label>Сколько создать</Form.Label>
                  </Col>
                  <Col>
                    <FormControl
                      type="number"
                      name="count"
                      placeholder="100"
                      onChange={(e) => updateFormField({ count: +e.target.value })}
                      defaultValue={promocodeForm.count}
                      className="mb-3"
                      required
                    />
                  </Col>
                </Row>
              </FormGroup>
            )}

            <FormGroup controlId="descForm">
              <Row>
                <Col xs={4}>
                  <Form.Label>Описание</Form.Label>
                </Col>
                <Col>
                  <FormControl
                    type="text"
                    name="description"
                    placeholder="Описание промокода"
                    onChange={(e) => updateFormField({ description: e.target.value })}
                    value={promocodeForm.description || editPromocode.description || ''}
                    className="mb-3"
                    required
                  />
                </Col>
              </Row>
            </FormGroup>

            <FormGroup controlId="discountForm">
              <Row>
                <Col xs={4}>
                  <Form.Label>Размер скидки</Form.Label>
                </Col>
                <Col>
                  <FormControl
                    type="number"
                    name="discount"
                    placeholder="750"
                    onChange={(e) => updateFormField({ discount: +e.target.value })}
                    value={promocodeForm.discount ?? editPromocode.discount ?? ''}
                    className="mb-3"
                    required
                  />
                </Col>
              </Row>
            </FormGroup>

            <FormGroup controlId="discountFormatForm">
              <Row>
                <Col xs={4}>
                  <Form.Label>Форма скидки</Form.Label>
                </Col>
                <Col>
                  <FormControl
                    as="select"
                    aria-label="discount"
                    name="discountFormat"
                    onChange={(e) => updateFormField({ discount_form: e.target.value })}
                    value={promocodeForm.discount_form || editPromocode.discount_form || 'percent'}
                    className="mb-3"
                    required
                  >
                    <option value="percent">Проценты</option>
                    <option value="rub">Рубли</option>
                  </FormControl>
                </Col>
              </Row>
            </FormGroup>

            <FormGroup controlId="availableСountForm">
              <Row>
                <Col xs={4}>
                  <Form.Label>Доступное количество</Form.Label>
                </Col>
                <Col>
                  <FormControl
                    type="number"
                    name="availableСount"
                    placeholder="10"
                    onChange={(e) => updateFormField({ available_count: +e.target.value })}
                    value={promocodeForm.available_count ?? editPromocode.available_count ?? ''}
                    min="0"
                    className="mb-3"
                    required
                  />
                </Col>
              </Row>
            </FormGroup>

            <FormGroup controlId="startDateForm">
              <Row>
                <Col xs={4}>
                  <Form.Label>Дата начала</Form.Label>
                </Col>
                <Col>
                  <FormControl
                    type="datetime-local"
                    name="start_date"
                    placeholder="start_date"
                    onChange={handleChangeDate}
                    value={startDateForm}
                    className="mb-3"
                    required
                  />
                </Col>
              </Row>
            </FormGroup>

            <FormGroup controlId="endDateForm">
              <Row>
                <Col xs={4}>
                  <Form.Label>Дата окончания</Form.Label>
                </Col>
                <Col>
                  <FormControl
                    type="datetime-local"
                    name="end_date"
                    placeholder="end_date"
                    onChange={handleChangeDate}
                    value={endDateForm}
                    className="mb-3"
                    required
                  />
                </Col>
              </Row>
            </FormGroup>

            <FormGroup controlId="timezoneForm">
              <Row>
                <Col xs={4}>
                  <Form.Label>Часовой пояс</Form.Label>
                </Col>
                <Col>
                  <Form.Control
                    className="mb-3"
                    disabled={!startDateForm && !endDateForm}
                    value={timezone}
                    onChange={onSelectTimezone}
                    as="select"
                    placeholder="select"
                  >
                    {timezones.map((timezone) => (
                      <option value={timezone.value} key={timezone.value}>
                        {timezone.name}
                      </option>
                    ))}
                  </Form.Control>
                </Col>
              </Row>
            </FormGroup>

            <FormGroup controlId="type">
              <Row>
                <Col xs={4}>
                  <Form.Label>Тип</Form.Label>
                </Col>
                <Col>
                  <FormControl
                    as="select"
                    name="type"
                    onChange={handleChangeTypes}
                    className="mb-3"
                    aria-label="type"
                    value={selectedPromoType}
                    disabled={isEditPromocode || !hasPromoRole}
                  >
                    {!hasPromoRole ? (
                      <option value="ticket">Билет</option>
                    ) : (
                      <>
                        <option>Выберите тип</option>
                        {PROMOCODE_TYPES.map((el, i) => (
                          <option key={`${el.value}${i}`} value={el.value}>
                            {el.label}
                          </option>
                        ))}
                      </>
                    )}
                  </FormControl>
                </Col>
              </Row>
            </FormGroup>

            {goods.length > 0 && selectedPromoType === 'product' && (
              <>
                <FormGroup
                  style={{
                    marginBottom: '20px',
                    marginTop: '20px',
                    display: 'flex',
                  }}
                >
                  <Col xs={6}>
                    <Form.Label>Товар</Form.Label>
                  </Col>
                  <Row>
                    <Button onClick={() => setProductModal(true)}>Добавить товар</Button>
                  </Row>
                </FormGroup>
                {renderSelectedItems(selectedProducts, 'product')}
              </>
            )}

            {insurances?.length > 0 && selectedPromoType === 'insurance' && (
              <>
                <FormGroup
                  style={{
                    marginBottom: '20px',
                    marginTop: '20px',
                    display: 'flex',
                  }}
                >
                  <Col xs={6}>
                    <Form.Label>Страховка</Form.Label>
                  </Col>
                  <Row>
                    <Button onClick={() => setInsuranceModal(true)}>Добавить страховку</Button>
                  </Row>
                </FormGroup>
                {renderSelectedItems(selectedInsurances, 'insurance')}
              </>
            )}

            {selectedPromoType === 'ticket' && !isEditPromocode && (
              <>
                <FormGroup controlId="eventForm">
                  <Row>
                    <Col xs={4}>
                      <Form.Label>Выберите событие</Form.Label>
                    </Col>
                    <Col>
                      {filteredEventsData?.length > 0 ? (
                        <FormControl
                          as="select"
                          aria-label="event"
                          name="event"
                          onChange={handleEvent}
                          defaultValue={isClonePromocode ? onePromoCode?.event_ids?.[0] : ''}
                          className="mb-3"
                          required
                        >
                          <option>Выберите событие</option>
                          {filteredEventsData.map((event) => (
                            <option value={event.public_id} key={event.public_id}>
                              {event.title}
                            </option>
                          ))}
                        </FormControl>
                      ) : (
                        <div className="mb-3 p-2 text-muted border rounded">
                          {hasPromoRole ? 'Нет доступных событий' : 'Нет доступных событий для администрирования'}
                        </div>
                      )}
                    </Col>
                  </Row>
                </FormGroup>

                {selectedEvent && !isEditPromocode && (
                  <FormGroup controlId="cityForm">
                    <Row>
                      <Col xs={4}>
                        <Form.Label>Выберите город</Form.Label>
                      </Col>
                      <Col>
                        <FormControl
                          as="select"
                          aria-label="event"
                          name="city"
                          onChange={handleChangeCity}
                          className="mb-3"
                          disabled={isEditPromocode}
                          defaultValue={isClonePromocode ? onePromoCode?.event_city_ids?.[0] : ''}
                          required
                        >
                          <option value="none">Выберите город</option>
                          <option value="all">Все города</option>
                          {selectedEvent?.event_city?.map((city) => (
                            <option value={city.public_id} key={city.public_id}>
                              {city.city.name_ru} — {moment(city.start_time).format('DD.MM.YYYY')}
                            </option>
                          ))}
                        </FormControl>
                      </Col>
                    </Row>
                  </FormGroup>
                )}

                {selectedEvent && formats.length > 0 && (
                  <FormGroup className="mb-2" controlId="formatsForm">
                    <Form.Label>Выберите форматы</Form.Label>
                    <FormControl as="select" multiple ref={selectFormatsRef} required={!isClonePromocode}>
                      {formats.map((format, index) => (
                        <option value={format.public_id} key={format.public_id + index}>
                          {format.title} - {format.address} - {moment(format.start_time).format('DD.MM.YYYY')}
                        </option>
                      ))}
                    </FormControl>
                  </FormGroup>
                )}

                {formats.length > 0 && (
                  <Row className="mb-3">
                    <Col />
                    <Col md="auto">
                      <Button onClick={handleSelectFormats}>Добавить</Button>
                    </Col>
                  </Row>
                )}

                {selectedFormats?.length > 0 && (
                  <p
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '20px',
                    }}
                  >
                    <span>
                      Выбранные форматы:{' '}
                      {selectedFormats.map((format) => (
                        <Tag onClick={() => handleDelSelectedFormat(format)} key={format?.public_id}>
                          {format.title} — {format.address}
                        </Tag>
                      ))}
                    </span>
                  </p>
                )}
              </>
            )}

            {selectedPromoType === 'ticket' && (
              <FormGroup controlId="limitForm">
                <Row>
                  <Col xs={4}>
                    <Form.Label>Лимиты</Form.Label>
                  </Col>
                  <Col>
                    <FormControl
                      as="select"
                      aria-label="limit"
                      name="limit"
                      onChange={handleLimit}
                      className="mb-3"
                      defaultValue={editPromocode?.kind}
                      required
                    >
                      {Object.values(Kind).map((ki) => (
                        <option value={ki} key={ki}>
                          {getKindName(ki)}
                        </option>
                      ))}
                    </FormControl>
                  </Col>
                </Row>
              </FormGroup>
            )}

            <FormGroup className="mt-3" controlId="activeForm">
              <Row>
                <Col>
                  <Form.Check
                    type="checkbox"
                    id={'activeCheck'}
                    label={'Действителен'}
                    onChange={(e) => updateFormField({ active: e.target.checked })}
                    defaultChecked={promocodeForm.active || editPromocode.active}
                  />
                </Col>
              </Row>
            </FormGroup>

            {isFormatsError && <p style={{ textAlign: 'center', color: 'red' }}>Выберите форматы</p>}
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={handleClosePopup}>
              Закрыть
            </Button>
            <Button type="submit" variant="primary">
              Сохранить
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>

      <ItemSelectionModal
        show={productModal}
        onHide={() => setProductModal(false)}
        items={goods}
        itemType="product"
        onChange={handleItemChange}
        onSave={handleSaveItems}
      />

      <ItemSelectionModal
        show={insuranceModal}
        onHide={() => setInsuranceModal(false)}
        items={insurances}
        itemType="insurance"
        onChange={handleItemChange}
        onSave={handleSaveItems}
      />
    </>
  )
}

export default ModalCreatePromocode
