import useAxios from 'axios-hooks'
import { useEffect, useState } from 'react'
import { But<PERSON>, Card, CardBody, Col, Modal, Row, Table } from 'react-bootstrap'
import { CSVLink } from 'react-csv'
import { Link } from 'react-router-dom'

import Loader from '@/components/Loader/Loader'

import { accessConfig, roles } from '@/accessConfig'
import { APIRoute, AppRoute, Role } from '@/const'
import { isAccessPermissions } from '@/utils/common'
import storage from '@/utils/storage'

import styles from './AdminsTable.module.scss'

export const AdminsTable = () => {
  const userStorage = storage.getUserObj()
  const [csvData, setCsvData] = useState({ headers: [], data: [] })
  const [isShowConfirmModal, setIsShowConfirmModal] = useState(false)
  const [selectedUser, setSelectedUser] = useState({})

  const [{ data, loading }, api] = useAxios(
    {
      url: APIRoute.USERS_ROLE,
      method: 'GET',
    },
    { manual: false }
  )

  const [, apiChangeRole] = useAxios(
    {
      url: APIRoute.SUPERADMIN_ROLE,
      method: 'PUT',
    },
    { manual: true }
  )

  useEffect(() => {
    if (data?.values?.length > 0) {
      generateCsvData(data.values)
    }
  }, [data])

  const generateCsvData = (values) => {
    const newHeaders = [
      { label: 'Почта', key: 'email' },
      { label: 'Имя', key: 'name' },
      { label: 'Фамилия', key: 'last_name' },
      ...Object.entries(roles).map(([key, value]) => ({ label: value, key })),
    ]
    const newData = values.map((item) => {
      return {
        email: item.email,
        name: item.name,
        last_name: item.last_name,
        ...Object.fromEntries(Object.keys(roles).map((key) => [key, item.role.includes(key) ? '✅' : ''])),
      }
    })

    setCsvData({
      headers: newHeaders,
      data: newData,
    })
  }

  const handleOpenConfirmModal = (roles, role, userId) => {
    setIsShowConfirmModal(true)

    const body = {}

    if (roles.includes(role)) {
      body.role = roles.filter((item) => item !== role)
    } else {
      body.role = [...roles, role]
    }

    setSelectedUser({ userId, body })
  }

  const handleToggleRole = () => {
    apiChangeRole({
      url: `${APIRoute.SUPERADMIN_ROLE}/${selectedUser.userId}`,
      data: selectedUser.body,
    }).then((r) => {
      if (r?.status === 200) {
        api()
        handleCloseConfirmModal()
      }
    })
  }

  const handleCloseConfirmModal = () => {
    setIsShowConfirmModal(false)
    setSelectedUser({})
  }

  return (
    <>
      <Row className="justify-content-end mb-4">
        <Col md="auto">
          <CSVLink data={csvData.data} headers={csvData.headers} filename={'admins-heroleague.csv'}>
            Скачать CSV
          </CSVLink>
        </Col>
      </Row>

      <Card>
        <CardBody className={styles.cardBody}>
          <Loader isLoading={loading && data?.values?.length === 0}>
            <Table className={styles.table}>
              <thead>
                <tr>
                  <th className={styles.th}>Почта</th>
                  <th className={styles.th}>Имя</th>
                  <th className={styles.th}>Фамилия</th>
                  {Object.entries(roles).map(
                    ([key, value]) =>
                      key !== Role.USER && (
                        <th className={styles.th} key={key}>
                          {value}
                        </th>
                      )
                  )}
                </tr>
              </thead>
              <tbody>
                {data?.values?.map((item) => (
                  <tr key={item.public_id}>
                    <td>
                      <Link to={`${AppRoute.USER_PROFILE}/${item.public_id}`} target="_blank">
                        {item.email}
                      </Link>
                    </td>
                    <td className={styles.tdName}>{item.name}</td>
                    <td className={styles.tdName}>{item.last_name}</td>
                    {Object.keys(roles).map(
                      (key) =>
                        key !== Role.USER && (
                          <td className={styles.th} key={key}>
                            {item.role.includes(key) ? (
                              <button
                                className={`${styles.toggle} ${
                                  isAccessPermissions(accessConfig.users.actions, userStorage.role)
                                    ? ''
                                    : styles.toggleDisabled
                                }`}
                                onClick={() => handleOpenConfirmModal(item.role, key, item.public_id)}
                                type="button"
                              >
                                ✅
                              </button>
                            ) : (
                              <button
                                className={`${styles.toggle} ${
                                  isAccessPermissions(accessConfig.users.actions, userStorage.role)
                                    ? ''
                                    : styles.toggleDisabled
                                }`}
                                onClick={() => handleOpenConfirmModal(item.role, key, item.public_id)}
                                type="button"
                              >
                                ☑
                              </button>
                            )}
                          </td>
                        )
                    )}
                  </tr>
                ))}
              </tbody>
            </Table>
          </Loader>
        </CardBody>
      </Card>

      <Modal show={isShowConfirmModal} onHide={handleCloseConfirmModal} size="sm">
        <Modal.Body>Изменить роль пользователя?</Modal.Body>
        <Modal.Footer>
          <Button onClick={handleToggleRole}>Да</Button>
          <Button onClick={handleCloseConfirmModal} variant="secondary">
            Нет
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  )
}
