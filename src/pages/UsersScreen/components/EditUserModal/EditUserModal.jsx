import useAxios from 'axios-hooks'
import _ from 'lodash'
import { useEffect, useState } from 'react'
import { Button, Col, Form, FormCheck, FormControl, FormGroup, Modal, Row } from 'react-bootstrap'
import Select from 'react-select'

import { roles } from '@/accessConfig'
import { APIRoute, Role } from '@/const'
import { useToast } from '@/hooks/useToast'
import { eventFilterSelectStyles } from '@/pages/EventsScreen/components/EventsFilter/shopSelectStyles'

export const EditUserModal = ({ selectedUser, isShowModal, onCloseModal, onUpdate }) => {
  const [formData, setFormData] = useState({ role: [Role.USER] })
  const [isChangedData, setChangedData] = useState(false)
  const [isPasswordInput, setIsPasswordInput] = useState(false)

  const roleOptions = Object.entries(roles).map(([key, value]) => ({ value: key, label: value }))

  const openToast = useToast()

  const [, api] = useAxios(
    {
      method: 'PUT',
    },
    { manual: true }
  )

  useEffect(() => {
    if (Object.keys(formData).length !== 0) {
      setChangedData(true)
    } else {
      setChangedData(false)
    }
  }, [formData])

  useEffect(() => {
    if (isShowModal && Object.keys(selectedUser).length > 0) {
      setFormData({})
    }
  }, [isShowModal, selectedUser])

  const sendData = (e) => {
    e.preventDefault()

    if (Object.keys(selectedUser)?.length > 0) {
      api({
        url: `${APIRoute.CUD_USER}/${selectedUser.public_id}`,
        method: 'PUT',
        data: formData,
      }).then((response) => {
        if (response.status === 200) {
          handleCloseModal()
          onUpdate()
          openToast.success({ message: 'Данные успешно обновлены' })
        }
      })
    } else {
      api({
        url: APIRoute.CUD_USER,
        method: 'POST',
        data: formData,
      }).then((response) => {
        if (response.status === 200) {
          handleCloseModal()
          onUpdate()
          openToast.success({ message: 'Пользователь успешно создан' })
        }
      })
    }
  }

  const handleChangeSelect = (items) => {
    setFormData({ ...formData, role: items.map((item) => item.value) })
  }

  const generateRoleOptions = (items) => {
    return items?.map((item) => {
      return { value: item, label: roles[item] || item }
    })
  }

  const handleCloseModal = () => {
    setFormData({ role: [Role.USER] })
    onCloseModal()
    setIsPasswordInput(false)
  }

  const handleChangePassFlag = (e) => {
    const isChecked = e.target.checked

    if (isChecked) {
      setIsPasswordInput(true)
    } else {
      setIsPasswordInput(false)

      setFormData((prev) => {
        const { password: _, ...newState } = prev
        return newState
      })
    }
  }

  const handleChangePassword = (e) => {
    const password = e.target.value

    if (password.length > 0) {
      setFormData({ ...formData, password })
    } else {
      setFormData((prev) => {
        const { password: _, ...newState } = prev
        return newState
      })
    }
  }

  return (
    <Modal show={isShowModal} onHide={handleCloseModal}>
      <Form onSubmit={sendData}>
        <Modal.Header closeButton>
          <Modal.Title>
            {Object.keys(selectedUser)?.length > 0 ? 'Редактирование пользователя' : 'Создание пользователя'}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <FormGroup controlId="nameForm">
            <Row>
              <Col xs={5}>
                <Form.Label>Имя</Form.Label>
              </Col>
              <Col>
                <FormControl
                  type="text"
                  name="name"
                  placeholder="Иван"
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  defaultValue={selectedUser.name}
                  className="mb-3"
                />
              </Col>
            </Row>
          </FormGroup>

          <FormGroup controlId="lastNameForm">
            <Row>
              <Col xs={5}>
                <Form.Label>Фамилия</Form.Label>
              </Col>
              <Col>
                <FormControl
                  type="text"
                  name="last_name"
                  placeholder="Иванов"
                  onChange={(e) => setFormData({ ...formData, last_name: e.target.value })}
                  defaultValue={selectedUser.last_name}
                  className="mb-3"
                />
              </Col>
            </Row>
          </FormGroup>

          <FormGroup controlId="secondForm">
            <Row>
              <Col xs={5}>
                <Form.Label>Отчество</Form.Label>
              </Col>
              <Col>
                <FormControl
                  type="text"
                  name="secondName"
                  placeholder="Иванович"
                  onChange={(e) => setFormData({ ...formData, second_name: e.target.value })}
                  defaultValue={selectedUser.second_name}
                  className="mb-3"
                />
              </Col>
            </Row>
          </FormGroup>

          <FormGroup controlId="genderForm">
            <Row>
              <Col xs={5}>
                <Form.Label>Пол</Form.Label>
              </Col>
              <Col>
                <FormControl
                  as="select"
                  aria-label="gender"
                  name="gender"
                  onChange={(e) => setFormData({ ...formData, gender: e.target.value })}
                  defaultValue={selectedUser.gender}
                  className="mb-3"
                >
                  <option>Выберите пол</option>
                  <option value="male">Мужской</option>
                  <option value="female">Женский</option>
                </FormControl>
              </Col>
            </Row>
          </FormGroup>

          <FormGroup controlId="phoneForm">
            <Row>
              <Col xs={5}>
                <Form.Label>Телефон</Form.Label>
              </Col>
              <Col>
                <FormControl
                  type="tel"
                  name="phone"
                  placeholder="79998887766"
                  onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                  defaultValue={selectedUser.phone}
                  className="mb-3"
                  minlength="11"
                  maxlength="11"
                />
              </Col>
            </Row>
          </FormGroup>

          <FormGroup controlId="birthDateForm">
            <Row>
              <Col xs={5}>
                <Form.Label>Дата рождения</Form.Label>
              </Col>
              <Col>
                <FormControl
                  type="date"
                  name="birthDate"
                  placeholder="birth_date"
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      birth_date: `${e.target.value}T00:00:00.000Z`,
                    })
                  }
                  defaultValue={selectedUser.birth_date}
                  className="mb-3"
                />
              </Col>
            </Row>
          </FormGroup>

          <FormGroup controlId="emailForm">
            <Row>
              <Col xs={5}>
                <Form.Label>Почта</Form.Label>
              </Col>
              <Col>
                <FormControl
                  type="email"
                  name="email"
                  placeholder="<EMAIL>"
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  defaultValue={selectedUser.email}
                  className="mb-3"
                  required
                />
              </Col>
            </Row>
          </FormGroup>

          <FormGroup className="mb-5" controlId="roleForm">
            <Row>
              <Col xs={5}>
                <Form.Label>Роли</Form.Label>
              </Col>
            </Row>
            <Row>
              <Col>
                <Select
                  isMulti
                  name="roles"
                  onChange={handleChangeSelect}
                  options={roleOptions}
                  defaultValue={generateRoleOptions(selectedUser?.role || formData?.role)}
                  styles={eventFilterSelectStyles}
                  className="basic-multi-select"
                  classNamePrefix="select"
                  placeholder="Выберите роли"
                  required
                />
              </Col>
            </Row>
          </FormGroup>

          <h5 className="mb-3">Экстренный контакт</h5>
          <FormGroup controlId="extraContactForm">
            <Row>
              <Col xs={5}>
                <Form.Label>Имя и Фамилия доверенного лица</Form.Label>
              </Col>
              <Col>
                <FormControl
                  type="tel"
                  name="extraContact"
                  placeholder="Иванна Иванова"
                  onChange={(e) => setFormData({ ...formData, extra_contact: e.target.value })}
                  defaultValue={selectedUser.extra_contact}
                  className="mb-3"
                />
              </Col>
            </Row>
          </FormGroup>

          <FormGroup controlId="contactPhoneForm">
            <Row>
              <Col xs={5}>
                <Form.Label>Контактный телефон</Form.Label>
              </Col>
              <Col>
                <FormControl
                  type="tel"
                  name="contactPhone"
                  placeholder="79998887766"
                  onChange={(e) => setFormData({ ...formData, contact_phone: e.target.value })}
                  defaultValue={selectedUser.contact_phone}
                  className="mb-3"
                  minlength="11"
                  maxlength="11"
                />
              </Col>
            </Row>
          </FormGroup>

          <FormGroup className="mb-4" controlId="extraContactForm">
            <Row>
              <Col xs={5}>
                <Form.Label>Отношения</Form.Label>
              </Col>
              <Col>
                <FormControl
                  as="select"
                  aria-label="typeRelate"
                  name="type_relate"
                  onChange={(e) => setFormData({ ...formData, type_relate: e.target.value })}
                  defaultValue={selectedUser.type_relate}
                  className="mb-3"
                >
                  <option>Выберите из списка</option>
                  <option value="Партнёр">Партнёр</option>
                  <option value="Родитель">Родитель</option>
                  <option value="Родственник">Родственник</option>
                  <option value="Друг">Друг</option>
                  <option value="Коллега">Коллега</option>
                </FormControl>
              </Col>
            </Row>
          </FormGroup>

          <Row>
            <Col className="mb-2" md={12}>
              <FormCheck
                inline
                label="Указать пароль"
                name="password"
                type="switch"
                id="passwordRadioGen"
                onChange={handleChangePassFlag}
              />
            </Col>

            {isPasswordInput && (
              <Col md={12}>
                <FormGroup controlId="passwordForm">
                  <Row>
                    <Col xs={5}>
                      <Form.Label>Пароль</Form.Label>
                    </Col>
                    <Col>
                      <FormControl
                        type="text"
                        name="password"
                        placeholder="*********"
                        onChange={handleChangePassword}
                        className="mb-3"
                      />
                    </Col>
                  </Row>
                </FormGroup>
              </Col>
            )}
          </Row>
        </Modal.Body>

        <Modal.Footer>
          <Button variant="secondary" onClick={handleCloseModal}>
            Закрыть
          </Button>
          <Button type="submit" variant="primary" disabled={!isChangedData}>
            Сохранить
          </Button>
        </Modal.Footer>
      </Form>
    </Modal>
  )
}
