import useAxios from 'axios-hooks'
import { useEffect, useState, useCallback } from 'react'
import { Button, Col, Container, Form, FormControl, FormGroup, ListGroup, Row } from 'react-bootstrap'
import { useNavigate, useParams } from 'react-router-dom'

import Layout from '@/components/Layout/Layout'
import QuillEditor from '@/components/QuillEditor/QuillEditor'

import { APIRoute, AppRoute, MAX_IMG_SIZE } from '@/const'
import { useToast } from '@/hooks/useToast'
import { NewsFormBanners } from '@/pages/NewsEditorScreen/components'
import { convertBase64 } from '@/utils/common'

const PagesFormScreen = () => {
  const [{ data: pages }, apiGetPages] = useAxios({
    url: APIRoute.GET_PAGES,
    method: 'GET',
  })
  const [, apiPage] = useAxios(
    {
      url: APIRoute.ACTIONS_PAGE,
      method: 'POST',
    },
    { manual: true }
  )

  const openToast = useToast()

  const [formData, setFormData] = useState({})
  const [isInvalidPublicId, setIsInvalidPublicId] = useState(null)
  const [defaultFormData, setDefaultFormData] = useState({
    banners: {},
  })

  const navigate = useNavigate()
  const { public_id } = useParams()

  const isUpdatePage = !!public_id

  useEffect(() => {
    if (isUpdatePage && pages?.length > 0) {
      const newsPage = pages.find((item) => item.public_id === public_id)
      setDefaultFormData({ ...newsPage })
      setFormData((prev) => ({ ...prev, old_public_id: public_id, ...newsPage }))
    } else if (isUpdatePage && pages?.length === 0) {
      apiGetPages()
    } else if (!isUpdatePage) {
      setFormData({
        active: true,
        text: '',
        subtitle: '',
        headline: '',
        publication_date: null,
      })
    }

    document.title = isUpdatePage ? 'Редактирование страницы' : 'Создание страницы'
  }, [pages, isUpdatePage, apiGetPages, public_id])

  const handleChangeFieldText = useCallback((evt) => {
    setFormData((prev) => ({ ...prev, [evt.target.name]: evt.target.value }))
  }, [])

  const handleChangePublicId = useCallback((evt) => {
    const regex = /^[a-z0-9_]+$/i
    const value = evt.target.value.toLowerCase()

    if (regex.test(value)) {
      setIsInvalidPublicId(false)
      setFormData((prev) => ({ ...prev, public_id: value }))
    } else if (value === '') {
      setIsInvalidPublicId(true)
      setFormData((prev) => ({ ...prev, public_id: value }))
    } else {
      setIsInvalidPublicId(true)
    }
  }, [])

  const handleChangeDate = useCallback((evt) => {
    const value = evt.target.value
    setFormData((prev) => ({
      ...prev,
      publication_date: new Date(value),
    }))
  }, [])

  const handleEditorChange = useCallback((html) => {
    setFormData((prev) => ({ ...prev, text: html }))
  }, [])

  const sendData = useCallback(
    (body) => {
      const method = isUpdatePage ? 'PUT' : 'POST'
      const toastText = isUpdatePage ? 'Страница изменена' : 'Страница создана'

      apiPage({ data: body, method: method }).then((r) => {
        if (r.status === 200 && r?.data?.message) {
          openToast.success({ message: toastText })
          navigate('/pages')
        }
      })
    },
    [isUpdatePage, apiPage, openToast, navigate]
  )

  const getEditorData = useCallback(
    (evt) => {
      evt.preventDefault()
      sendData(formData)
    },
    [formData, sendData]
  )

  const handleChangeActive = useCallback((e) => {
    setFormData((prev) => ({ ...prev, active: e.target.checked }))
  }, [])

  const handleBannerUpload = useCallback(
    async (bannerType, file) => {
      const fileSizeInB = file.size
      const base64 = await convertBase64(file)

      if (base64 === '') {
        setFormData((prev) => ({
          ...prev,
          banners: { ...prev.banners, [bannerType]: '' },
        }))
      } else if (fileSizeInB <= MAX_IMG_SIZE) {
        setFormData((prev) => ({
          ...prev,
          banners: { ...prev.banners, [bannerType]: base64 },
        }))
      } else {
        openToast.error({
          title: true,
          message: `Файл слишком большой: ${file.name}`,
          duration: 6000,
        })
      }
    },
    [openToast]
  )

  const handleDeletePictures = useCallback((field) => {
    setFormData((prev) => {
      const newData = { ...prev }

      if (newData.banners) {
        const newBanners = { ...newData.banners }
        delete newBanners[field]

        if (Object.keys(newBanners).length === 0) {
          delete newData.banners
        } else {
          newData.banners = newBanners
        }
      }

      return newData
    })
  }, [])

  return (
    <Layout title={isUpdatePage ? 'Редактирование страницы' : 'Создание страницы'}>
      <Container>
        <Form onSubmit={getEditorData} className="mb-5">
          <Row className="mb-3">
            <ListGroup>
              <ListGroup.Item>
                <FormGroup className="mb-0">
                  <Row>
                    <Col xs={2}>
                      <Form.Label>Заголовок:</Form.Label>
                    </Col>
                    <Col>
                      <FormControl
                        type="text"
                        name="headline"
                        placeholder="Введите заголовок"
                        onChange={handleChangeFieldText}
                        defaultValue={defaultFormData.headline}
                        required
                      />
                    </Col>
                  </Row>
                </FormGroup>
              </ListGroup.Item>
              <ListGroup.Item>
                <FormGroup className="mb-0">
                  <Row>
                    <Col xs={2}>
                      <Form.Label>Подзаголовок:</Form.Label>
                    </Col>
                    <Col>
                      <FormControl
                        type="text"
                        name="subtitle"
                        placeholder="Введите подзаголовок"
                        onChange={handleChangeFieldText}
                        defaultValue={defaultFormData.subtitle}
                      />
                    </Col>
                  </Row>
                </FormGroup>
              </ListGroup.Item>
              <ListGroup.Item>
                <FormGroup className="mb-0">
                  <Row>
                    <Col xs={2}>
                      <Form.Label>Идентификатор:</Form.Label>
                    </Col>
                    <Col>
                      <FormControl
                        type="text"
                        name="public_id"
                        placeholder="Введите идентификатор"
                        onChange={handleChangePublicId}
                        value={isUpdatePage ? defaultFormData.public_id : formData.public_id}
                        isInvalid={isInvalidPublicId}
                        disabled={isUpdatePage}
                        maxLength={16}
                      />
                      <span className="small fw-light">
                        * разрешены цифры &quot;0-9&quot;, латинские буквы &quot;a-z&quot;, символ &quot;_&quot; и не
                        более 16 символов
                      </span>
                    </Col>
                  </Row>
                </FormGroup>
              </ListGroup.Item>
              <ListGroup.Item>
                <FormGroup controlId="dateForm">
                  <Row>
                    <Col xs={2}>
                      <Form.Label>Дата публикации:</Form.Label>
                    </Col>
                    <Col>
                      <FormControl type="datetime-local" name="publication_date" onChange={handleChangeDate} />
                    </Col>
                  </Row>
                </FormGroup>
              </ListGroup.Item>
              <ListGroup.Item>
                <Form.Check
                  type="checkbox"
                  label="Показывать"
                  name="active"
                  onChange={handleChangeActive}
                  checked={formData?.active || false}
                />
              </ListGroup.Item>
            </ListGroup>
          </Row>
        </Form>
      </Container>
      <Container>
        <FormGroup>
          <Form.Label>Содержание:</Form.Label>
          <QuillEditor
            value={formData.text || ''}
            onChange={handleEditorChange}
            placeholder="Введите содержание страницы"
          />
        </FormGroup>
        <NewsFormBanners
          banners={formData.banners || defaultFormData.banners}
          onUpload={handleBannerUpload}
          onDelete={handleDeletePictures}
        />
        <Button variant="primary" type="submit" form="editor-form" className="mt-3">
          {isUpdatePage ? 'Обновить' : 'Создать'}
        </Button>
      </Container>
    </Layout>
  )
}

export default PagesFormScreen
