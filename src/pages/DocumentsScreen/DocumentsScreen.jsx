import { useCallback, useEffect, useState } from 'react'
import { ButtonGroup, Card, Col, Dropdown, Form, Row, Spinner, Tab, Table, Tabs } from 'react-bootstrap'
import { Link } from 'react-router-dom'

import AdvancedPagination from '@/components/AdvancedPagination/AdvancedPagination'
import Layout from '@/components/Layout/Layout'
import Loader from '@/components/Loader/Loader'
import ConfirmDeleteModal from '@/components/Modal/ConfirmDeleteModal/ConfirmDeleteModal'
import PageSearch from '@/components/PageSearch/PageSearch'
import TableTemplate from '@/components/TableTemplate/TableTemplate'

import { AppRoute } from '@/const'
import {
  useCreateNewSection,
  useDeleteSectionsList,
  useGetSectionLists,
  useGetUserDocuments,
  useUpdateMyDocument,
  useUpdateSectionsList,
  useDeleteDocument,
  useGetAllDocuments,
  useGetSiteDocuments,
  useUploadDocument,
} from '@/features/files/api'
import { convertBase64, times } from '@/utils/common'

import { MyDocumentPopup } from './components/MyDocumentPopup'
import { SectionPopup } from './components/SectionPopup'
import { documentsSectionsTableData, documentsTableData, documentsUserTableData } from './documentsScreenData'

const PAGE_LIMIT_PAGINATION = 50

const UpdateButton = ({ item }) => {
  const { mutateAsync: uploadDocument, isPending: isLoadingUploadFile } = useUploadDocument()

  const handleChangeFile = async (evt) => {
    const file = evt.target.files[0]
    const base64 = await convertBase64(file)
    const section = evt.target.name
    const data = {
      file: base64,
      section: {
        public_id: section,
      },
    }

    await uploadDocument(data)
  }

  return (
    <>
      {isLoadingUploadFile ? (
        <Spinner animation="border" size="sm" />
      ) : (
        <label className="text-primary" htmlFor={`siteDocsFile${item.public_id}`}>
          Обновить
        </label>
      )}
      <input
        onChange={handleChangeFile}
        id={`siteDocsFile${item.public_id}`}
        className="visually-hidden"
        type="file"
        name={item.section.public_id}
      />
    </>
  )
}

function DocumentsScreen() {
  const [selectedTab, setSelectedTab] = useState('sections')

  const { data: sectionsData, isLoading: isLoadingSections } = useGetSectionLists()
  const { data: userFilesData, isLoading: isLoadingUserDocuments } = useGetUserDocuments(selectedTab === 'userDocs')
  const { mutateAsync: createSection } = useCreateNewSection()
  const { mutateAsync: updateSection } = useUpdateSectionsList()
  const { mutateAsync: deleteSection } = useDeleteSectionsList()
  const { mutateAsync: updateDocument } = useUpdateMyDocument()
  const { mutateAsync: deleteDocument } = useDeleteDocument()
  const { data: allDocumentsData, isLoading: isLoadingAllFiles } = useGetAllDocuments(
    0,
    PAGE_LIMIT_PAGINATION,
    selectedTab === 'allDocs'
  )
  const { data: siteDocumentsData, isLoading: isLoadingSiteDocs } = useGetSiteDocuments(selectedTab === 'siteDocs')
  const sections = sectionsData?.data?.values
  const userFiles = userFilesData?.data?.values
  const allFilesData = allDocumentsData?.data

  const [confirmModal, setConfirmModal] = useState(false)
  const [sectionConfirmModal, setSectionConfirmModal] = useState(false)
  const [selectedDeleteItemId, setSelectedDeleteItemId] = useState('')
  const [sectionDeleteItemId, setSectionDeleteItemId] = useState('')
  const [filteredUserFiles, setFilteredUserFiles] = useState(null)
  const [filterByName, setFilterByName] = useState([])
  const [filterByNameAllDocs, setFilterByNameAllDocs] = useState([])
  const [filteredSections, setFilteredSections] = useState([])
  const [filteredAllFiles, setFilteredAllFiles] = useState([])
  const [allFiles, setAllFiles] = useState([])
  const [isChangeSectionPopup, setChangeSectionPopup] = useState(false)
  const [currentSection, setCurrentSection] = useState(null)
  const [newNameSection, setNewNameSection] = useState('')
  const [isNewSection, setIsNewSection] = useState(false)
  const [publicId, setPublicId] = useState('all')
  const [publicIdAllDocs, setPublicIdAllDocs] = useState('all')
  const [isChangeDocumentPopup, setIsChangeDocumentPopup] = useState(false)
  const [currentDocument, setCurrentDocument] = useState(null)

  const handleSelectTab = (tab) => {
    setSelectedTab(tab)
  }

  const handleConfirmDelete = (public_id) => {
    setSelectedDeleteItemId(public_id)
    setConfirmModal(true)
  }

  const handleSectionDelete = (public_id) => {
    setSectionDeleteItemId(public_id)
    setSectionConfirmModal(true)
  }

  const handleDeleteItem = () => {
    setConfirmModal(false)

    deleteDocument(selectedDeleteItemId, {
      onSuccess: () => {
        setSelectedDeleteItemId('')
      },
    })
  }

  const handleConfirmChange = (item) => {
    setIsNewSection(false)
    setCurrentSection(item)
    setChangeSectionPopup(true)
  }

  const handleChangeItem = () => {
    updateSection({ name: newNameSection, public_id: currentSection.public_id })
    setChangeSectionPopup(false)
  }

  const handleChangeDocument = (newDocument) => {
    updateDocument(newDocument)
    setIsChangeDocumentPopup(false)
  }

  const handleAddSection = () => {
    setIsNewSection(true)
    setChangeSectionPopup(true)
  }

  const handleNewSection = () => {
    if (newNameSection.length) {
      createSection({ name: newNameSection })
      setChangeSectionPopup(false)
    }
  }

  const handleDeleteSection = () => {
    deleteSection(sectionDeleteItemId)
    setSectionConfirmModal(false)
  }

  const handleDocumentChange = (item) => {
    setCurrentDocument(item)
    setIsChangeDocumentPopup(true)
  }

  const returnActionsSectionTable = (item) => {
    return (
      <td className="text-center" onClick={(evt) => evt.stopPropagation()}>
        <Dropdown>
          <Dropdown.Toggle bsPrefix=" " variant="outline-secondary" id="dropdown-basic">
            <i className="bi bi-three-dots" />
          </Dropdown.Toggle>

          <Dropdown.Menu>
            <Dropdown.Item as="button" onClick={() => handleConfirmChange(item)}>
              <i className="bi bi-pencil" /> Изменить
            </Dropdown.Item>
            <Dropdown.Item className="text-danger" as="button" onClick={() => handleSectionDelete(item.public_id)}>
              <i className="bi bi-trash" /> Удалить
            </Dropdown.Item>
          </Dropdown.Menu>
        </Dropdown>
      </td>
    )
  }

  const filterSelect = useCallback(
    (public_id) => {
      setPublicId(public_id)
      if (public_id === 'all') {
        setFilterByName(filteredUserFiles)
      } else {
        setFilterByName(filteredUserFiles.filter((el) => el?.section?.public_id === public_id))
      }
    },
    [filteredUserFiles]
  )

  const filterAllDocumentSelect = useCallback(
    (public_id) => {
      setPublicIdAllDocs(public_id)
      if (public_id === 'all') {
        setFilterByNameAllDocs(allFiles)
      } else {
        setFilterByNameAllDocs(allFiles.filter((el) => el?.section?.public_id === public_id))
      }
    },
    [allFiles]
  )

  useEffect(() => {
    if (allFiles && filterByName.length === 0) {
      filterAllDocumentSelect('all')
    } else if (allFiles && filterByName.length > 0) {
      filterAllDocumentSelect(publicIdAllDocs)
    }
  }, [allFiles, filterAllDocumentSelect, filterByName.length, publicIdAllDocs])

  useEffect(() => {
    if (filteredUserFiles && filterByName.length === 0) {
      filterSelect('all')
    } else if (filteredUserFiles && filterByName.length > 0) {
      filterSelect(publicId)
    }
  }, [filteredUserFiles, filterByName.length, filterSelect, publicId])

  const returnActionsTable = (item) => {
    return (
      <td className="text-center" onClick={(evt) => evt.stopPropagation()}>
        <Dropdown>
          <Dropdown.Toggle bsPrefix=" " variant="outline-secondary" id="dropdown-basic">
            <i className="bi bi-three-dots" />
          </Dropdown.Toggle>

          <Dropdown.Menu>
            <Dropdown.Item as="button" onClick={() => handleDocumentChange(item)}>
              <i className="bi bi-pencil" /> Изменить
            </Dropdown.Item>
            <Dropdown.Item className="text-danger" as="button" onClick={() => handleConfirmDelete(item.public_id)}>
              <i className="bi bi-trash" /> Удалить
            </Dropdown.Item>
          </Dropdown.Menu>
        </Dropdown>
      </td>
    )
  }

  return (
    <Layout
      title="Документы"
      onClickAddButton={selectedTab === 'sections' && handleAddSection}
      href={selectedTab === 'userDocs' && 'documents/form'}
    >
      <Row className="mb-3">
        <Col>
          <Tabs defaultActiveKey="sections" className="mb-3" onSelect={handleSelectTab}>
            <Tab title="Разделы" eventKey="sections">
              <Loader isLoading={isLoadingSections}>
                {sections && sections.length > 0 ? (
                  <>
                    <Row className="mb-3">
                      <Col>
                        <PageSearch values={sections} setValues={setFilteredSections} />
                      </Col>
                    </Row>

                    <TableTemplate
                      data={documentsSectionsTableData}
                      values={filteredSections}
                      actions={returnActionsSectionTable}
                      minWidth="700px"
                      maxWidth="1000px"
                    />
                  </>
                ) : (
                  <p>Нет документов</p>
                )}
              </Loader>
            </Tab>
            <Tab title="Мои документы" eventKey="userDocs">
              <Loader isLoading={isLoadingUserDocuments}>
                {userFiles && userFiles.length > 0 ? (
                  <>
                    <Row className="mb-3">
                      <Col>
                        <PageSearch values={userFiles} setValues={setFilteredUserFiles} />
                        <Row>
                          <Col style={{ marginTop: '20px', display: 'flex', gap: '5px' }}>
                            <b>Фильтр: </b> <Form.Label htmlFor="inputPassword5"> по разделу</Form.Label>
                            <Form.Select
                              onChange={(e) => filterSelect(e.target.value)}
                              style={{ width: '150px' }}
                              name="status"
                              aria-label="Фильтр по разделу"
                            >
                              <option value="all">все</option>
                              {sections &&
                                sections.map((el) => (
                                  <option key={el.public_id} value={el.public_id}>
                                    {el.name}
                                  </option>
                                ))}
                            </Form.Select>
                          </Col>
                        </Row>
                      </Col>
                    </Row>

                    <TableTemplate
                      isCustomLink
                      data={documentsUserTableData}
                      values={filterByName}
                      actions={returnActionsTable}
                      minWidth="1800"
                    />
                  </>
                ) : (
                  <p>Нет документов</p>
                )}
              </Loader>
            </Tab>
            <Tab title="Все документы" eventKey="allDocs">
              <Loader isLoading={isLoadingAllFiles}>
                {allFilesData?.values ? (
                  <>
                    <Row className="mb-3">
                      <Col>
                        <PageSearch values={allFilesData?.values} setValues={setFilteredAllFiles} />
                        <Row>
                          <Col style={{ marginTop: '20px', display: 'flex', gap: '5px' }}>
                            <b>Фильтр: </b> <Form.Label htmlFor="inputPassword5"> по разделу</Form.Label>
                            <Form.Select
                              onChange={(e) => filterAllDocumentSelect(e.target.value)}
                              style={{ width: '150px' }}
                              name="statusAllDocument"
                              aria-label="Фильтр по разделу"
                            >
                              <option value="all">все</option>
                              {sections &&
                                sections.map((el) => (
                                  <option key={el.public_id} value={el.public_id}>
                                    {el.name}
                                  </option>
                                ))}
                            </Form.Select>
                          </Col>
                        </Row>
                      </Col>
                    </Row>

                    <TableTemplate
                      isCustomLink
                      data={documentsTableData}
                      values={filterByNameAllDocs}
                      minWidth="1800"
                    />

                    <Row>
                      <Col className="pt-3">
                        <AdvancedPagination values={filteredAllFiles} setValues={setAllFiles} limitValues={20} />
                      </Col>
                    </Row>
                  </>
                ) : (
                  <p>Нет документов</p>
                )}
              </Loader>
            </Tab>
            <Tab title="Сайт" eventKey="siteDocs">
              <Loader isLoading={isLoadingSiteDocs}>
                {siteDocumentsData?.data?.values?.length > 0 ? (
                  <Card>
                    <Card.Body>
                      <Table>
                        <thead>
                          <tr>
                            <th>Тип</th>
                            <th>Дата загрузки</th>
                            <th>Пользователь</th>
                            <th />
                            <th />
                          </tr>
                        </thead>
                        <tbody>
                          {siteDocumentsData?.data?.values?.map((item) => (
                            <tr key={item.public_id}>
                              <td>{item.title}</td>
                              <td>{times.getFullDate(item.created_date)}</td>
                              <td>
                                <Link to={`${AppRoute.USER_PROFILE}/${item.user_public_id}`}>
                                  {item.user_public_id}
                                </Link>
                              </td>
                              <td>
                                <a
                                  href={`/api/files/document/${item.section.public_id}`}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                >
                                  Открыть
                                </a>
                              </td>
                              <td>
                                <UpdateButton item={item} />
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </Table>
                    </Card.Body>
                  </Card>
                ) : (
                  <p style={{ textAlign: 'center' }}>Нет документов</p>
                )}
              </Loader>
            </Tab>
          </Tabs>
        </Col>
      </Row>

      <ConfirmDeleteModal
        isShow={confirmModal}
        onClose={setConfirmModal}
        onUpdateSelectedDeleteItem={setSelectedDeleteItemId}
        onDeleteItem={handleDeleteItem}
        text="Удалить документ"
      />
      <ConfirmDeleteModal
        isShow={sectionConfirmModal}
        onClose={setSectionConfirmModal}
        onUpdateSelectedDeleteItem={setSectionDeleteItemId}
        onDeleteItem={handleDeleteSection}
        text="Удалить раздел"
      />

      <SectionPopup
        isChangeSectionPopup={isChangeSectionPopup}
        setChangeSectionPopup={setChangeSectionPopup}
        isNewSection={isNewSection}
        handleNewSection={handleNewSection}
        handleChangeItem={handleChangeItem}
        setNewNameSection={setNewNameSection}
        currentSection={currentSection}
      />

      <MyDocumentPopup
        isChangeDocumentPopup={isChangeDocumentPopup}
        setIsChangeDocumentPopup={setIsChangeDocumentPopup}
        handleChangeDocument={handleChangeDocument}
        currentDocument={currentDocument}
        sections={sections}
      />
    </Layout>
  )
}

export default DocumentsScreen
