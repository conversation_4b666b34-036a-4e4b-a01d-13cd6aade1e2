import useAxios from 'axios-hooks'
import { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>,
  Col,
  Figure,
  FloatingLabel,
  Form,
  FormControl,
  Modal,
  OverlayTrigger,
  Row,
} from 'react-bootstrap'

import { renderBtnTooltip } from '@/utils/tooltips.jsx'

import SuccessModal from '../../../../components/Modal/SuccessModal/SuccessModal'
import PhotoViewer from '../../../../components/PhotoViewer/PhotoViewer'
import { APIRoute, MAX_IMG_SIZE } from '../../../../const'
import { useToast } from '../../../../hooks/useToast'
import { convertBase64 } from '../../../../utils/common'
import { getImageSrc } from '../../../../utils/images'
import styles from '../ShopCategories/ShopCategories.module.scss'

function Collections() {
  const [{ data: collections }, apiCollections] = useAxios({
    url: APIRoute.ACTIONS_COLLECTION,
    method: 'GET',
  })
  const [, api] = useAxios({
    url: APIRoute.ACTIONS_COLLECTION,
    method: 'GET',
  })

  const openToast = useToast()

  const [isShowFormModal, setIsShowFormModal] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState({})
  const [formData, setFormData] = useState({})
  const [showSuccessModal, setShowSuccessModal] = useState(false)
  const [confirmModal, setConfirmModal] = useState(false)

  const handleClickAddCategory = () => {
    setIsShowFormModal(true)
  }

  const handleChangeField = (evt) => {
    const name = evt.target.name
    const value = name === 'price' ? +evt.target.value : evt.target.value
    const newFormData = { ...formData }

    if (value === '') {
      delete newFormData[name]
      setFormData({ ...newFormData })
    } else {
      setFormData({ ...formData, [name]: value })
    }
  }

  const handleSubmitForm = () => {
    const method = Object.keys(selectedCategory).length > 0 ? 'PUT' : 'POST'
    const url =
      Object.keys(selectedCategory).length > 0
        ? `${APIRoute.ACTIONS_COLLECTION}/${selectedCategory.public_id}`
        : APIRoute.ACTIONS_COLLECTION

    api({ url: url, method: method, data: formData }).then((r) => {
      if (r.status === 200) {
        handleCloseFormModal()
        openToast.success({ message: 'Коллекция сохранена' })
        apiCollections()
        setFormData({})
      }
    })
  }

  const handleConfirmDelete = (category) => {
    setConfirmModal(true)
    setSelectedCategory(category)
  }

  const handleCloseConfirm = () => {
    setConfirmModal(false)
    setSelectedCategory({})
  }

  const handleDeleteOrder = () => {
    api({
      url: `${APIRoute.ACTIONS_COLLECTION}/${selectedCategory.public_id}`,
      method: 'DELETE',
    }).then((r) => {
      if (r.status === 200) {
        apiCollections()
        setConfirmModal(false)
        openToast.success({ message: 'Коллекция удалена' })
        setSelectedCategory({})
      }
    })
  }

  const handleClickEdit = (category) => {
    setSelectedCategory(category)
    setIsShowFormModal(true)
  }

  const handleCloseFormModal = () => {
    setIsShowFormModal(false)
    setSelectedCategory({})
    setFormData({})
  }

  const handleFileRead = async (evt) => {
    const file = evt.target.files[0]
    const fileSizeInB = file.size
    const base64 = await convertBase64(file)

    const newFormData = { ...formData }

    if (base64 === '') {
      newFormData.pivture = ''
      setFormData({ ...newFormData })
    } else if (fileSizeInB <= MAX_IMG_SIZE) {
      setFormData({ ...newFormData, image: base64 })
    } else if (fileSizeInB > MAX_IMG_SIZE) {
      openToast.error({ message: `Файл слишком большой: ${file.name}`, duration: 6000 })
      evt.target.value = ''
    }
  }

  const handleDeletePictures = (evt) => {
    const copyFormData = { ...formData }

    delete copyFormData.image

    setFormData({ ...copyFormData })
    evt.target.value = ''
  }

  return (
    <>
      <Row className="mb-4">
        <Col />
        <Col md="auto">
          <Button onClick={handleClickAddCategory} variant="success">
            <i className="bi bi-plus-circle me-2" />
            Добавить
          </Button>
        </Col>
      </Row>

      <Row className="g-2">
        {collections?.values?.map((category, index) => (
          <Col md={6} key={category.public_id}>
            <Card className={`${styles.card} p-2`}>
              <Row>
                <Col className={`${styles.indexWrapper} d-grid align-items-center justify-content-center`} md="auto">
                  {index + 1}
                </Col>
                <Col className="p-0" md="auto">
                  <PhotoViewer className={styles.pictureContainer}>
                    {category?.image?.length > 0 && (
                      <a
                        href={getImageSrc(category.image)}
                        data-pswp-width={800}
                        data-pswp-height={300}
                        key={category.image + '-' + index}
                        target="_blank"
                        rel="noreferrer"
                        className={styles.imageLink}
                      >
                        <img className={styles.picture} src={getImageSrc(category.image)} alt="" />
                      </a>
                    )}
                  </PhotoViewer>
                </Col>
                <Col className="d-flex align-items-center">
                  <h2 className={styles.title}>{category.title}</h2>
                </Col>
              </Row>

              <Row className={styles.buttons}>
                <Col className={styles.btnWrapper} md={12}>
                  <OverlayTrigger
                    placement="left"
                    delay={{ show: 250, hide: 400 }}
                    overlay={(evt) => renderBtnTooltip(evt, 'редактировать')}
                  >
                    <Button onClick={() => handleClickEdit(category)} className={styles.btnEdit} type="button" />
                  </OverlayTrigger>
                </Col>
                <Col className={styles.btnWrapper} md={12}>
                  <OverlayTrigger
                    placement="left"
                    delay={{ show: 250, hide: 400 }}
                    overlay={(evt) => renderBtnTooltip(evt, 'удалить')}
                  >
                    <Button
                      onClick={() => handleConfirmDelete(category)}
                      className={`${styles.btnEdit} ${styles.btnDelete}`}
                      type="button"
                    />
                  </OverlayTrigger>
                </Col>
              </Row>
            </Card>
          </Col>
        ))}
      </Row>

      <Modal show={isShowFormModal} onHide={handleCloseFormModal}>
        <Modal.Header closeButton>
          <Modal.Title>
            {Object.keys(selectedCategory).length > 0 ? 'Изменение коллекции' : 'Добавление коллекции'}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Row className="g-3 mb-3">
            <Col md={12}>
              <FloatingLabel controlId="titleProductLabel" label="Название коллекции">
                <FormControl
                  onChange={handleChangeField}
                  defaultValue={selectedCategory.title}
                  name="title"
                  type="text"
                  placeholder="Название коллекции"
                  style={{ backgroundColor: 'rgba(0, 0, 0, 0.1)' }}
                />
              </FloatingLabel>
            </Col>
            <Col className="mb-2">
              <Form.Group controlId="formPictureFile">
                <Form.Control
                  className={`${styles.inputFile} visually-hidden`}
                  type="file"
                  onChange={handleFileRead}
                  name="image"
                  accept=".jpg, .png, .jpeg"
                />
                <Form.Label className={styles.fileButton}>Выбрать изображение</Form.Label>
              </Form.Group>
            </Col>
            {(formData.image || selectedCategory.image) && (
              <Col className="d-grid justify-content-center" md={12}>
                <Figure className="m-0">
                  <Figure.Image
                    className="m-0"
                    width={95}
                    alt="171x180"
                    src={getImageSrc(formData.image || selectedCategory.image)}
                  />
                </Figure>
                {formData.image && (
                  <Button onClick={handleDeletePictures} variant="link" size="sm">
                    удалить
                  </Button>
                )}
              </Col>
            )}
          </Row>
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={handleSubmitForm}>Сохранить</Button>
        </Modal.Footer>
      </Modal>

      <SuccessModal
        show={showSuccessModal}
        handleCloseModal={setShowSuccessModal}
        description="Данные успешно сохранены."
      />

      <Modal show={confirmModal} onHide={handleCloseConfirm}>
        <Modal.Header closeButton>
          <Modal.Title>Удалить коллекцию?</Modal.Title>
        </Modal.Header>
        <Modal.Footer>
          <Button variant="link" onClick={handleCloseConfirm}>
            Отменить
          </Button>
          <Button variant="danger" onClick={handleDeleteOrder}>
            Удалить
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  )
}

export default Collections
