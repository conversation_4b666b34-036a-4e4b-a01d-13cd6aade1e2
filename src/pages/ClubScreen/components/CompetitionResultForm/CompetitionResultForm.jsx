import { useMask } from '@react-input/mask'
import useAxios from 'axios-hooks'
import moment from 'moment/moment'
import React from 'react'
import { Col, FloatingLabel, Form, FormControl, FormGroup, Row } from 'react-bootstrap'
import { useForm, Controller } from 'react-hook-form'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { removeEmptyString } from '@/utils/common'
import { checkSetValue } from '@/utils/forms'
import { getImageSrc } from '@/utils/images'

import styles from '../../../CorpScreen/components/CompaniesForm/CompaniesForm.module.scss'

const formatSecondsToTime = (seconds) => {
  const h = String(Math.floor(seconds / 3600)).padStart(2, '0')
  const m = String(Math.floor((seconds % 3600) / 60)).padStart(2, '0')
  const s = String(seconds % 60).padStart(2, '0')
  return `${h}:${m}:${s}`
}

const formatTimeToSeconds = (time) => {
  const [h, m, s] = time.split(':').map(Number)
  return h * 3600 + m * 60 + s
}

const isValidTime = (time) => {
  const [h, m, s] = time.split(':').map(Number)
  return !(isNaN(h) || isNaN(m) || isNaN(s))
}

function MaskedInput({ value, onChange, isInvalid, className, placeholder }) {
  const inputRef = useMask({
    mask: '99:99:99',
    replacement: { 9: /\d/ },
  })

  return (
    <FormControl
      ref={inputRef}
      type="text"
      value={value || ''}
      onChange={onChange}
      isInvalid={isInvalid}
      placeholder={placeholder}
      className={className}
    />
  )
}

function CompetitionResultForm({ selectedItem, competitionsType, onUpdateValues, onCloseModal }) {
  const {
    control,
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      passing_time: selectedItem?.passing_time ? formatSecondsToTime(selectedItem.passing_time) : '',
      event_date: moment(selectedItem?.event_date).format('YYYY-MM-DD'),
      competition_type: {
        public_id: selectedItem?.competition_type?.public_id,
      },
      valid: selectedItem?.valid,
    },
  })

  const openToast = useToast()

  const isEdit = Object.keys(selectedItem).length > 0

  const [, api] = useAxios(
    {
      url: `${APIRoute.UD_CLUB_COMPETITION}/${selectedItem.public_id}`,
      method: 'PUT',
    },
    { manual: true }
  )

  const onSubmit = (data) => {
    const filteredData = removeEmptyString(data)
    const filteredDataSecond = removeEmptyString(filteredData)

    if ('passing_time' in filteredDataSecond) {
      filteredDataSecond.passing_time = formatTimeToSeconds(data.passing_time)
    }

    if (Object.keys(filteredDataSecond).length > 0) {
      api({ data: filteredDataSecond }).then((r) => {
        if (r.status === 200) {
          onUpdateValues()
          onCloseModal()
          openToast.success({ message: 'Результат успешно изменён' })
        }
      })
    }
  }

  return (
    <Form onSubmit={handleSubmit(onSubmit)} id="form">
      <Row className="g-3 mb-3">
        <Row className="g-3">
          {Array.isArray(selectedItem.picture) &&
            selectedItem.picture?.map((item) => (
              <Col key={item}>
                <img src={getImageSrc(item)} width={100} style={{ borderRadius: '6px' }} alt="" />
              </Col>
            ))}
        </Row>

        <Col md={12}>
          <Form.Group>
            <FloatingLabel controlId="passingTimeInput" label="Результат">
              <Controller
                name="passing_time"
                control={control}
                rules={{ required: true, validate: (v) => isValidTime(v) }}
                render={({ field: { onChange, value } }) => (
                  <MaskedInput
                    value={value}
                    onChange={onChange}
                    isInvalid={errors.passing_time}
                    placeholder="Результат"
                    className={styles.field}
                  />
                )}
              />
            </FloatingLabel>
          </Form.Group>
        </Col>

        <Col md={12}>
          <Form.Group>
            <FloatingLabel controlId="eventDateInput" label="Дата">
              <FormControl
                className={styles.field}
                {...register('event_date', {
                  required: !isEdit,
                  setValueAs: (v) => checkSetValue(v, selectedItem?.event_date, 'date'),
                })}
                type="date"
                isInvalid={errors.event_date}
                placeholder="Дата"
              />
            </FloatingLabel>
          </Form.Group>
        </Col>

        <Col md={12}>
          <Form.Group>
            <FloatingLabel controlId="competitionTypeLabel" label="Тип соревнования">
              <FormControl
                className={styles.field}
                as="select"
                {...register('competition_type.public_id', {
                  setValueAs: (v) => checkSetValue(v, selectedItem?.competition_type?.public_id, 'text'),
                })}
                type="text"
                placeholder="Тип соревнования"
              >
                <option value="">Выберите тип</option>
                {competitionsType?.map((item) => (
                  <option value={item.public_id} key={item.public_id}>
                    {item.title}
                  </option>
                ))}
              </FormControl>
            </FloatingLabel>
          </Form.Group>
        </Col>

        <Col md={12}>
          <FormGroup controlId="validForm">
            <Form.Check
              type="checkbox"
              id={'valid'}
              label={'Подтвердить результат'}
              {...register('valid', {
                setValueAs: (v) => checkSetValue(v, selectedItem?.valid, 'checkbox'),
              })}
            />
          </FormGroup>
        </Col>
      </Row>
    </Form>
  )
}

export default CompetitionResultForm
