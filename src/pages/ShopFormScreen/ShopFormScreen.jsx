import useAxios from 'axios-hooks'
import { createRef, useEffect, useMemo, useRef, useState } from 'react'
import { Button, Col, Figure, FloatingLabel, Form, FormControl, FormSelect, Row } from 'react-bootstrap'
import { Link, useNavigate, useParams } from 'react-router-dom'
import Select from 'react-select'
import AsyncSelect from 'react-select/async'

import { APIRoute, MAX_IMG_SIZE } from '@/const'
import { useGetCountryList } from '@/features/cities/api/getCountryList'
import { useGetEvents } from '@/features/events/api/getEvents'
import { useGetProcessingList } from '@/features/processing/api/getProcessingList'
import { useGetShopCategories } from '@/features/shop/api/getShopCategories'
import { useGetShopCollections } from '@/features/shop/api/getShopCollections'
import { useGetShopItems } from '@/features/shop/api/getShopItems'
import { useToast } from '@/hooks/useToast'
import { convertBase64, getDefaultOptionsSelect, getOptionsSelect } from '@/utils/common'
import { unixToMoment } from '@/utils/date'
import { updateFormData } from '@/utils/forms'
import { getImageSrc } from '@/utils/images'

import ProductTabs from './components/ProductTabs'
import { shopSelectStyles } from './components/shopSelectStyles'
import styles from './ShopFormScreen.module.scss'
import Layout from '../../components/Layout/Layout'
import { Tag } from '../ShopScreen/components/Delivery/Delivery'

const groupProducts = [
  {
    name: 'Одежда',
    value: 'clothes',
  },
  {
    name: 'Аксессуары',
    value: 'accessories',
  },
  {
    name: 'Головные уборы',
    value: 'hats',
  },
]

const genderProducts = [
  {
    name: 'Унисекс',
    value: 'unisex',
  },
  {
    name: 'Женский',
    value: 'female',
  },
  {
    name: 'Мужской',
    value: 'male',
  },
]

const projects = [
  {
    id: 1,
    value: 'heroes',
    label: 'Лига Героев',
  },
  {
    id: 2,
    value: 'triathlon',
    label: 'Триатлон',
  },
  {
    id: 3,
    value: 'ocr',
    label: 'OCR',
  },
]

function ShopFormScreen() {
  const { data: shopItemsData } = useGetShopItems()
  const { data: categoriesData } = useGetShopCategories()
  const { data: collectionsData } = useGetShopCollections()

  const data = shopItemsData?.data
  const categories = categoriesData?.data
  const collections = collectionsData?.data

  const [, api] = useAxios(
    {
      url: APIRoute.SHOP_PRODUCT,
      method: 'POST',
    },
    { manual: true }
  )

  const getProcessingListQuery = useGetProcessingList()
  const processingList = getProcessingListQuery?.data?.data?.values

  const openToast = useToast()
  const { data: eventsData } = useGetEvents()
  const events = useMemo(() => eventsData?.data?.values || [], [eventsData?.data?.values])
  const { data: countriesData, isSuccess: countriesLoaded } = useGetCountryList()
  const countries = countriesData?.data?.values || []

  const [formData, setFormData] = useState({})
  const [defaultFormData, setDefaultFormData] = useState({})
  const [isOldPrice, setIsOldPrice] = useState(false)
  const [selectedEvent, setSelectedEvent] = useState(null)
  const [selectedCities, setSelectedCities] = useState([])
  const [allCities, setAllCities] = useState([])
  const selectCityRef = useRef()
  const { public_id } = useParams()
  const navigate = useNavigate()

  const picturesInputRef = createRef()

  const isUpdateShopProduct = !!public_id
  const pictures = formData?.pictures || defaultFormData?.pictures

  useEffect(() => {
    if (events.length) {
      const newCities = []

      events.forEach((el) => {
        el?.event_city?.forEach((city) => {
          defaultFormData?.event_city?.forEach((item) => {
            if (item === city?.public_id) {
              newCities.push(city)
              return true
            }
          })
        })
      })

      if (defaultFormData?.event_city?.length > 0) {
        const eventPublicId = defaultFormData.event_city[0].split('_')[0]

        const findEvent = events.find((item) => item.public_id === eventPublicId)

        setSelectedEvent(findEvent)
      }

      if (newCities.length) setSelectedCities(newCities)
    }
  }, [events, defaultFormData.event_city])

  useEffect(() => {
    if (public_id && data) {
      const findProduct = data.values.find((item) => item.public_id === public_id)

      if (findProduct) {
        setDefaultFormData({ ...findProduct })

        if (findProduct?.old_price) {
          setIsOldPrice(true)
        }
      }
    } else if (data?.values?.length > 0) {
      setFormData((prev) => ({
        ...prev,
        priority_number: data.values[0].priority_number + 1,
        delivery: true,
        public: true,
        retailcrm: true,
      }))
    }
  }, [public_id, data, defaultFormData.event_city])

  const loadCountryOptions = (inputValue) => {
    return new Promise((resolve) => {
      // Небольшая задержка для лучшего UX
      setTimeout(() => {
        if (!countries.length) {
          resolve([])
          return
        }

        const filteredCountries = !inputValue
          ? countries
          : countries.filter(
              (country) =>
                country.name_ru.toLowerCase().includes(inputValue.toLowerCase()) ||
                country.name_en.toLowerCase().includes(inputValue.toLowerCase())
            )

        const options = filteredCountries.map((country) => ({
          value: country.id,
          label: country.name_ru,
        }))

        resolve(options)
      }, 100)
    })
  }

  const handleSubmitForm = (evt) => {
    evt.preventDefault()

    const method = isUpdateShopProduct ? 'PUT' : 'POST'
    const toastText = isUpdateShopProduct ? 'Товар изменён' : 'Товар создан'
    const url = isUpdateShopProduct ? `${APIRoute.SHOP_PRODUCT}/${defaultFormData.public_id}` : APIRoute.SHOP_PRODUCT

    const fetch = () => {
      api({ url: url, data: formData, method: method }).then((r) => {
        if (r.status === 200) {
          openToast.success({ message: toastText })
          navigate('/shop')
        }
      })
    }

    fetch()
  }

  const handleChangeField = (evt) => {
    updateFormData(evt, formData, setFormData, defaultFormData)
  }

  const handleChangeSelect = (evt) => {
    const value = evt.target.value
    const name = evt.target.name

    setFormData({ ...formData, [name]: { public_id: value } })
  }

  const handleEvent = (e) => {
    const event = events.find((event) => event.public_id === e.target.value)

    setSelectedEvent(event)
  }

  const handleSelectCity = () => {
    const selectedOptions = selectCityRef.current.selectedOptions
    const findCities = []

    setSelectedCities(allCities)

    for (let i = 0; i < selectedOptions.length; i++) {
      const option = selectedOptions[i].value

      if (option === 'none' || option === 'all') return
      else {
        const findCity = selectedEvent?.event_city?.find((item) => item.public_id === option)

        if (!selectedCities.map((item) => item.public_id).includes(option)) {
          findCities.push(findCity)
        }
      }

      const newCities = [...selectedCities, ...findCities]
      const idCities = newCities.map((item) => item.public_id)

      setFormData({ ...formData, event_city: idCities })
      setSelectedCities(newCities)
    }
  }

  const handleChangeCity = (evt) => {
    if (evt.target.value === 'all') {
      setAllCities(selectedEvent?.event_city)
      const newCities = selectedEvent?.event_city
      const idCities = newCities?.map((item) => item.public_id)

      setFormData({ ...formData, event_city: idCities })
    }
  }

  const handleDelSelectedCity = (city) => {
    const filteredCities = selectedCities.filter((item) => item.public_id !== city.public_id)
    const idCities = filteredCities.map((city) => city.public_id)

    setFormData({ ...formData, event_city: idCities })
    setSelectedCities(filteredCities)
  }

  const handleChangeToggle = (evt) => {
    const name = evt.target.name
    const value = evt.target.checked

    setFormData({ ...formData, [name]: value })
  }

  const handleFileRead = async (event) => {
    const file = event.target.files[0]
    const fileSizeInKB = file.size
    const base64 = await convertBase64(file)

    const newFormData = { ...formData }

    if (base64 === '') {
      delete newFormData.picture
      setFormData({ ...newFormData })
    } else if (fileSizeInKB <= MAX_IMG_SIZE) {
      setFormData({ ...formData, picture: base64 })
    } else if (fileSizeInKB > MAX_IMG_SIZE) {
      openToast.error({
        title: true,
        message: `Файл слишком большой: ${file.name}`,
        duration: 6000,
      })
    }
  }

  const handleMorePicture = async (event) => {
    const files = event.target.files
    const copyPictures = formData.pictures ? [...formData.pictures] : []
    let bigFiles = ''
    const filterSizeFiles = Object.values(files).filter((file) => {
      if (file.size <= MAX_IMG_SIZE) {
        return file
      } else if (file.size > MAX_IMG_SIZE) {
        bigFiles += `${file.name}, `
      }
    })
    const filteredValues = await Promise.all(filterSizeFiles.map(async (file) => await convertBase64(file)))

    setFormData({ ...formData, pictures: [...copyPictures, ...filteredValues] })
    bigFiles.length > 0 &&
      openToast.error({
        title: true,
        message: `Размер слишком большой: ${bigFiles}`,
        duration: 6000,
      })
  }

  const handleDeletePictures = (index) => {
    const newPictures = [...formData.pictures]

    newPictures.splice(index, 1)

    setFormData({ ...formData, pictures: [...newPictures] })

    if (newPictures.length === 0) picturesInputRef.current.value = ''
  }

  const movePictureInList = (index, toIndex) => {
    const newPictures = [...formData.pictures]
    const element = newPictures.splice(index, 1)[0]

    newPictures.splice(toIndex, 0, element)

    setFormData({ ...formData, pictures: [...newPictures] })
  }

  const handleChangeCollect = (evt) => {
    const newFormData = { ...formData }

    if (evt.length > 0) {
      const collections = evt.map((item) => item.value)
      setFormData({ ...formData, collections: collections })
    } else {
      delete newFormData.collections
      setFormData({ ...newFormData })
    }
  }

  const handleChangeCountry = (selectedOption) => {
    if (selectedOption) {
      setFormData({ ...formData, country: { id: parseInt(selectedOption.value) } })
    } else {
      const newFormData = { ...formData }
      delete newFormData.country
      setFormData(newFormData)
    }
  }

  const handleChangeCheckbox = (evt) => {
    const isChecked = evt.target.checked
    const name = evt.target.name
    const value = evt.target.value
    const newCalendarStory = { ...formData }
    let newArr = []

    if (name in formData) {
      newArr = [...formData[name]]
    } else if (name in defaultFormData) {
      newArr = [...defaultFormData[name]]
    }

    if (isChecked) {
      newArr.push(value)
      setFormData({ ...formData, [name]: [...newArr] })
    } else {
      const filteredArr = newArr.filter((item) => item !== value)

      if (filteredArr.length === 0) {
        delete newCalendarStory[name]
        setFormData({ ...newCalendarStory })
      } else {
        setFormData({ ...formData, [name]: [...filteredArr] })
      }
    }
  }

  const checkCheckbox = (name, value) => {
    if (formData[name]) {
      return formData[name] && formData[name].includes(value)
    } else {
      return defaultFormData[name] && defaultFormData[name].includes(value)
    }
  }

  const handleChangeOldPrice = (evt) => {
    const value = +evt.target.value
    const newFormData = { ...formData }

    if (value > 0) {
      setFormData({ ...formData, old_price: value })
    } else if (value === defaultFormData?.old_price) {
      delete newFormData.old_price
      setFormData({ ...newFormData })
    } else if (value === 0 && defaultFormData?.old_price > 0) {
      setFormData({ ...formData, old_price: 0 })
    }
  }

  const handleToggleOldPrice = (evt) => {
    const isChecked = evt.target.checked
    const newFormData = { ...formData }

    setIsOldPrice(isChecked)

    if (!isChecked && defaultFormData?.old_price) {
      setFormData({ ...formData, old_price: 0 })
    } else if (!isChecked && !defaultFormData?.old_price) {
      delete newFormData.old_price
      setFormData({ ...newFormData })
    } else if (isChecked && defaultFormData?.old_price) {
      delete newFormData.old_price
      setFormData({ ...newFormData })
    }
  }

  const renderPicture = (item, index, array) => {
    return (
      <Col className="d-grid" md="auto" key={item}>
        <Figure className="m-0">
          <Figure.Caption className="m-1">Доп картинка {index + 1}</Figure.Caption>
          <Figure.Image className="m-0" width={171} alt="171x180" src={getImageSrc(item)} />
        </Figure>
        {array === formData?.pictures && (
          <Row>
            <Col>
              <Button
                onClick={() => movePictureInList(index, index - 1)}
                variant="link"
                size="sm"
                disabled={index === 0}
              >
                {'<'}
              </Button>
            </Col>
            <Col>
              <Button onClick={() => handleDeletePictures(index)} variant="link" size="sm">
                удалить
              </Button>
            </Col>
            <Col>
              <Button
                onClick={() => movePictureInList(index, index + 1)}
                variant="link"
                size="sm"
                disabled={index === formData?.pictures?.length - 1}
              >
                {'>'}
              </Button>
            </Col>
          </Row>
        )}
      </Col>
    )
  }

  return (
    <Layout>
      <Row className="mb-3">
        <Col>
          <Button as={Link} to={'/shop'} variant="outline-secondary">
            <i className="bi bi-arrow-left me-2" />
            Вернуться к списку товаров
          </Button>
        </Col>
      </Row>
      <Row className="mb-3">
        <h3>{isUpdateShopProduct ? 'Редактирование товара' : 'Добавление товара'}</h3>
      </Row>

      <Form className="mb-5" onSubmit={handleSubmitForm} id="productForm">
        <Row className="g-3 mb-3">
          <Col md={4}>
            <FloatingLabel controlId="titleProductLabel" label="Название товара">
              <FormControl
                onChange={handleChangeField}
                defaultValue={defaultFormData.title}
                name="title"
                type="text"
                placeholder="Название товара"
              />
            </FloatingLabel>
          </Col>

          <Col md={4}>
            <FloatingLabel controlId="materialProductLabel" label="Материал товара">
              <FormControl
                onChange={handleChangeField}
                defaultValue={defaultFormData.material}
                name="material"
                type="text"
                placeholder="Материал товара"
              />
            </FloatingLabel>
          </Col>

          <Col md={4}>
            <AsyncSelect
              key={`country-select-${defaultFormData?.country?.id || 'empty'}-${countriesLoaded ? 'ready' : 'loading'}`}
              cacheOptions
              defaultOptions={countriesLoaded}
              loadOptions={loadCountryOptions}
              defaultValue={
                defaultFormData?.country
                  ? {
                      value: defaultFormData.country.id,
                      label: defaultFormData.country.name_ru,
                    }
                  : null
              }
              onChange={handleChangeCountry}
              styles={shopSelectStyles}
              name="country"
              className="basic-select"
              classNamePrefix="select"
              placeholder="Страна происхождения"
              isClearable
              noOptionsMessage={() => 'Нет доступных стран'}
              loadingMessage={() => 'Загрузка стран...'}
            />
          </Col>

          <Col md={4}>
            <FloatingLabel controlId="priceProductLabel" label="Цена товара в рублях">
              <FormControl
                onChange={handleChangeField}
                defaultValue={defaultFormData.price}
                name="price"
                type="number"
                placeholder="Цена товара в рублях"
                required
              />
            </FloatingLabel>
          </Col>

          <Col md={4}>
            <FloatingLabel controlId="groupProductLabel" label="Раздел">
              <FormSelect
                onChange={handleChangeField}
                name="group"
                aria-label="Раздел"
                value={formData?.group || defaultFormData?.group || ''}
              >
                <option value="">выберите один из вариантов</option>
                {groupProducts.map((item) => (
                  <option value={item.value} key={item.value}>
                    {item.name}
                  </option>
                ))}
              </FormSelect>
            </FloatingLabel>
          </Col>

          <Col md={4}>
            <FloatingLabel controlId="subgroupProductLabel" label="Подраздел, напр.: Футболки">
              <FormControl
                onChange={handleChangeField}
                defaultValue={defaultFormData.subgroup}
                name="subgroup"
                type="text"
                placeholder="Подраздел, напр.: Футболки"
              />
            </FloatingLabel>
          </Col>

          <Col md={4}>
            <FloatingLabel controlId="genderProductLabel" label="Пол">
              <FormSelect
                onChange={handleChangeField}
                name="gender"
                aria-label="Пол"
                value={formData?.gender || defaultFormData?.gender || ''}
              >
                <option value="">выберите один из вариантов</option>
                {genderProducts.map((item) => (
                  <option value={item.value} key={item.value}>
                    {item.name}
                  </option>
                ))}
              </FormSelect>
            </FloatingLabel>
          </Col>

          <Col md={4}>
            <FloatingLabel controlId="publicIdLabelOrder" label="Юридическое лицо">
              <FormSelect
                onChange={handleChangeSelect}
                name="processing"
                aria-label="processing_public_id"
                value={formData?.processing?.public_id || defaultFormData?.processing?.public_id || ''}
              >
                <option value="">выберите один из вариантов</option>
                {processingList?.map((item) => (
                  <option value={item.public_id} key={item.public_id}>
                    {item.entity}
                  </option>
                ))}
              </FormSelect>
            </FloatingLabel>
          </Col>

          <Col md={4}>
            <FloatingLabel controlId="categoryLabelProduct" label="Категория товара">
              <FormSelect
                onChange={handleChangeSelect}
                value={formData?.category?.public_id || defaultFormData?.category?.public_id || ''}
                name="category"
                aria-label="Категория товара"
              >
                <option value="">выберите один из вариантов</option>
                {categories?.values?.map((item) => (
                  <option value={item.public_id} key={item.public_id}>
                    {item.title}
                  </option>
                ))}
              </FormSelect>
            </FloatingLabel>
          </Col>

          <Col md={4}>
            {collections?.values?.length > 0 && (
              <Select
                defaultValue={getDefaultOptionsSelect(defaultFormData?.collections)}
                onChange={handleChangeCollect}
                styles={shopSelectStyles}
                isMulti
                name="collections"
                options={getOptionsSelect(collections?.values, 'title', 'title')}
                className="basic-multi-select"
                classNamePrefix="select"
                placeholder="Коллекции"
                closeMenuOnSelect={false}
              />
            )}
          </Col>

          <Col md={4}>
            <FloatingLabel controlId="priorityNumberProductLabel" label="Приоритет">
              <FormControl
                onChange={handleChangeField}
                value={formData?.priority_number || defaultFormData?.priority_number || ''}
                name="priority_number"
                type="number"
                placeholder="Приоритет"
              />
            </FloatingLabel>
          </Col>

          <Col md={4}>
            <FloatingLabel controlId="vendorCodeProductLabel" label="Артикул">
              <FormControl
                onChange={handleChangeField}
                value={formData?.vendor_code || defaultFormData?.vendor_code || ''}
                name="vendor_code"
                type="text"
                placeholder="Артикул"
              />
            </FloatingLabel>
          </Col>

          <Col className="d-flex align-items-center" md="auto">
            <Form.Check
              onChange={handleChangeToggle}
              checked={Boolean(formData.delivery ?? defaultFormData.delivery)}
              name="delivery"
              type="switch"
              id="delivery-switch"
              label="Доставка"
            />
          </Col>

          <Col className="d-flex align-items-center" md="auto">
            <Form.Check
              onChange={handleChangeToggle}
              checked={Boolean(formData.public ?? defaultFormData.public)}
              name="public"
              type="switch"
              id="public-switch"
              label="Показывать в каталоге"
            />
          </Col>

          <Col md={4}>
            <Row>
              <Col md="auto">Магазин:</Col>
              {projects.map(({ value, label }) => (
                <Col md="auto" key={value}>
                  <Form.Check
                    type="checkbox"
                    id={value}
                    label={label}
                    name="project"
                    value={value}
                    onChange={handleChangeCheckbox}
                    checked={checkCheckbox('project', value)}
                  />
                </Col>
              ))}
            </Row>
          </Col>

          <Col className="d-flex align-items-center" md={8}>
            <Row>
              <Col md="auto">
                <Form.Check
                  onChange={handleChangeToggle}
                  checked={Boolean(formData.retailcrm ?? defaultFormData.retailcrm)}
                  name="retailcrm"
                  type="switch"
                  id="retailcrm-switch"
                  label="Синхронизация со складом"
                />
              </Col>
              <Col md="auto" style={{ marginLeft: '20px' }}>
                <Form.Check
                  onChange={handleChangeToggle}
                  checked={Boolean(formData.text_field ?? defaultFormData.text_field)}
                  name="text_field"
                  type="switch"
                  id="text_field-switch"
                  label="Поле для текст"
                />
              </Col>
              <Col md="auto">
                <Form.Check
                  onChange={handleChangeToggle}
                  checked={Boolean(formData.is_related ?? defaultFormData.is_related)}
                  name="is_related"
                  type="switch"
                  id="is_related-switch"
                  label="Сопутствующий товар"
                />
              </Col>
            </Row>
          </Col>
          <Col md={6}>
            <Form.Group controlId="formFile">
              <Form.Label className={styles.fileButton}>Выбрать главное изображение</Form.Label>
              <Form.Control
                className="visually-hidden"
                type="file"
                onChange={handleFileRead}
                accept=".jpg, .png, .jpeg, .webp"
              />
            </Form.Group>
            <span className="small">* максимальный размер изображений 5MB</span>
          </Col>

          <Col md={6}>
            <Form.Group controlId="formMoreFile">
              <Form.Label className={styles.fileButton}>Выбрать дополнительные изображения</Form.Label>
              <Form.Control
                className="visually-hidden"
                type="file"
                onChange={handleMorePicture}
                ref={picturesInputRef}
                accept=".jpg, .png, .jpeg, .webp"
                multiple
              />
            </Form.Group>
          </Col>
        </Row>

        <Row className="mb-3">
          {(formData?.picture || defaultFormData?.picture) && (
            <Col md="auto">
              <Figure>
                <Figure.Caption className="m-1">Картинка предпросмотр</Figure.Caption>
                <Figure.Image
                  width={171}
                  alt="Картинка предпросмотр"
                  src={getImageSrc(formData?.picture || defaultFormData?.picture)}
                />
              </Figure>
            </Col>
          )}
          {pictures?.map((item, index) => renderPicture(item, index, pictures))}
        </Row>

        <Row className="mb-3">
          <Col md={8} className={styles.descriptionWrap}>
            <FloatingLabel controlId="floatingTextareaDescription" label="Описание товара">
              <Form.Control
                onChange={handleChangeField}
                defaultValue={defaultFormData.description}
                as="textarea"
                placeholder="Описание товара"
                name="description"
                style={{ height: '140px' }}
              />
            </FloatingLabel>
          </Col>
          <Col>
            <Row>
              <Col className="d-flex align-items-center m-3" md="12">
                <Form.Check
                  onChange={handleToggleOldPrice}
                  name="old_price"
                  type="switch"
                  id="old-price-switch"
                  label="Показывать скидку"
                  checked={Boolean(isOldPrice)}
                />
              </Col>
              <Col>
                {isOldPrice && (
                  <FloatingLabel controlId="oldPriceProductLabel" label="Старая цена">
                    <FormControl
                      onChange={handleChangeOldPrice}
                      defaultValue={defaultFormData?.old_price}
                      name="old_price"
                      type="number"
                      placeholder="Старая цена"
                    />
                  </FloatingLabel>
                )}
              </Col>
            </Row>
          </Col>
        </Row>
      </Form>

      <h4 className="mb-2">Сопутствующие товары</h4>

      <div style={{ maxWidth: '700px', marginTop: '40px', marginBottom: '40px' }}>
        <Row>
          <Col xs={4}>
            <Form.Label>Выберите событие</Form.Label>
          </Col>
          <Col>
            <Form.Control
              as="select"
              aria-label="event"
              name="event"
              onChange={handleEvent}
              className="mb-3"
              value={selectedEvent?.public_id}
              required
            >
              <option>Выберите событие</option>
              {events.map((event) => (
                <option value={event?.public_id} key={event?.public_id}>
                  {event?.title}
                </option>
              ))}
            </Form.Control>
          </Col>
        </Row>

        {selectedEvent && (
          <Row>
            <Col xs={4}>
              <Form.Label>Выберите город</Form.Label>
            </Col>
            <Col>
              <Form.Control
                as="select"
                aria-label="event"
                name="city"
                multiple
                ref={selectCityRef}
                onChange={handleChangeCity}
                className="mb-3"
                required
              >
                <option value="none">Выберите город</option>
                <option value="all">Все города</option>
                {selectedEvent?.event_city?.map((city) => (
                  <option value={city.public_id} key={city.public_id}>
                    {city.city.name_ru} — {unixToMoment(city.start_time).format('DD.MM.YYYY')}
                  </option>
                ))}
              </Form.Control>
            </Col>
          </Row>
        )}

        {selectedEvent && (
          <Row className="mb-3">
            <Col />
            <Col md="auto">
              <Button onClick={handleSelectCity}>Добавить</Button>
            </Col>
          </Row>
        )}

        {selectedCities?.length > 0 && (
          <p style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
            <span>
              Выбранные города:{' '}
              {selectedCities.map((city) => (
                <Tag onClick={() => handleDelSelectedCity(city)} key={city?.public_id}>
                  {city.address}
                </Tag>
              ))}
            </span>
          </p>
        )}
      </div>

      <Row className="mt-4 mb-5">
        <Col className="d-grid" md={{ offset: 4, span: 4 }}>
          <Button type="submit" form="productForm" variant="success" size="lg">
            Сохранить
          </Button>
        </Col>
      </Row>

      <h3 className="mb-3">Редактирование опций</h3>

      <ProductTabs productId={public_id} />
    </Layout>
  )
}

export default ShopFormScreen
