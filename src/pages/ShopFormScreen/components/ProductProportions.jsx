import useAxios from 'axios-hooks'
import { useCallback, useEffect, useState, useRef } from 'react'
import { Button, Col, FloatingLabel, InputGroup, Row, Card, Alert, Form } from 'react-bootstrap'
import { useParams, useSearchParams } from 'react-router-dom'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import ProportionModal from '@/pages/ShopScreen/components/ProportionModal/ProportionModal'
import { updateFormData } from '@/utils/forms'

function ProportionCard({ defaultFormData, onUpdateOptions }) {
  const [formData, setFormData] = useState({})
  const [count, setCount] = useState(defaultFormData?.count ?? 0)
  const [proportionCount, setProportionCount] = useState('')
  const { public_id } = useParams()
  const openToast = useToast()

  const [{ loading: isLoading }, apiProportion] = useAxios(
    {
      url: APIRoute.PRODUCT_PROPORTION,
      method: 'PUT',
    },
    { manual: true }
  )

  const [, apiCountProportion] = useAxios(
    {
      url: APIRoute.PRODUCT_PROPORTION,
      method: 'PUT',
    },
    { manual: true }
  )

  const [, apiDeleteProportion] = useAxios(
    {
      url: APIRoute.PRODUCT_PROPORTION,
      method: 'DELETE',
    },
    { manual: true }
  )

  const handleSubmitProportion = () => {
    if (Object.keys(formData)?.length > 0) {
      apiProportion({
        url: `${APIRoute.PRODUCT_PROPORTION}/${defaultFormData.public_id}`,
        data: formData,
      }).then((r) => {
        if (r.status === 200) {
          openToast.success({ message: `Опция ${defaultFormData.name} изменена` })
        }
      })
    }
  }

  const handleSubmitCountProportions = () => {
    const value = Number(proportionCount)

    if (value !== 0) {
      apiCountProportion({
        url: `${APIRoute.PRODUCT_PROPORTION}/${defaultFormData.public_id}/${value}`,
      }).then((r) => {
        if (r.status === 200) {
          openToast.success({ message: `Остаток опции ${defaultFormData.name} изменён` })
          setCount((prev) => prev + value)
          setProportionCount('')
        }
      })
    }
  }

  const handleChangeField = (evt) => {
    updateFormData(evt, formData, setFormData, defaultFormData)
  }

  const handleUpdateCount = (evt) => {
    if (public_id) {
      setProportionCount(evt.target.value)
    } else {
      setProportionCount(evt.target.value)
    }
  }

  const handleDeleteProportion = () => {
    apiDeleteProportion({
      url: `${APIRoute.PRODUCT_PROPORTION}/${defaultFormData.public_id}`,
    }).then((r) => {
      if (r.status === 200) {
        openToast.success({ message: 'Опция удалена' })
        onUpdateOptions()
      }
    })
  }

  return (
    <Col md={4} className="mb-3">
      <Card className="h-100">
        <Card.Header as="h6">{defaultFormData?.name || 'Новая опция'}</Card.Header>
        <Card.Body>
          <Form>
            <Form.Group className="mb-3">
              <FloatingLabel controlId={`type-${defaultFormData?.public_id}`} label="Тип опции">
                <Form.Control
                  onChange={handleChangeField}
                  defaultValue={defaultFormData?.type}
                  value={formData.type}
                  name="type"
                  type="text"
                  placeholder="Тип опции"
                  required
                />
              </FloatingLabel>
            </Form.Group>

            <Form.Group className="mb-3">
              <FloatingLabel controlId={`name-${defaultFormData?.public_id}`} label="Название элемента опции">
                <Form.Control
                  onChange={handleChangeField}
                  defaultValue={defaultFormData?.name}
                  value={formData.name}
                  name="name"
                  type="text"
                  placeholder="Название элемента опции"
                  required
                />
              </FloatingLabel>
            </Form.Group>

            <Form.Group className="mb-3">
              <FloatingLabel controlId={`vendor-${defaultFormData?.public_id}`} label="Артикул опции">
                <Form.Control
                  onChange={handleChangeField}
                  defaultValue={defaultFormData?.vendor_code}
                  value={formData.vendor_code}
                  name="vendor_code"
                  type="text"
                  placeholder="Артикул опции"
                  required
                />
              </FloatingLabel>
            </Form.Group>

            <InputGroup className="mb-3">
              <FloatingLabel controlId={`count-${defaultFormData?.public_id}`} label="Остаток" className="flex-grow-1">
                <Form.Control
                  value={proportionCount}
                  onChange={handleUpdateCount}
                  name="count"
                  type="number"
                  placeholder=""
                />
              </FloatingLabel>
              <Button
                onClick={handleSubmitCountProportions}
                disabled={isLoading || proportionCount === ''}
                variant="outline-primary"
              >
                Добавить
              </Button>
            </InputGroup>

            <Alert variant="info" className="py-1 px-2 text-center mb-0">
              <small>
                Всего: <strong>{count}</strong>
              </small>
            </Alert>
          </Form>
        </Card.Body>
        <Card.Footer className="d-flex justify-content-between">
          <Button
            onClick={handleSubmitProportion}
            variant="success"
            size="sm"
            disabled={Object.keys(formData)?.length === 0 && proportionCount === ''}
          >
            Сохранить
          </Button>
          <Button onClick={handleDeleteProportion} variant="danger" size="sm">
            Удалить
          </Button>
        </Card.Footer>
      </Card>
    </Col>
  )
}

function ProductProportions({ productId }) {
  const [isOpenProportionModal, setIsOpenProportionModal] = useState(false)
  const [searchParams, setSearchParams] = useSearchParams()
  const proportionsRef = useRef(null)

  const [{ data: productProportions }, api] = useAxios(
    {
      url: APIRoute.PRODUCT_PROPORTION_LIST,
      method: 'GET',
    },
    { manual: true }
  )

  const getProportions = useCallback(() => {
    api({ url: `${APIRoute.PRODUCT_PROPORTION_LIST}/${productId}` })
  }, [api, productId])

  useEffect(() => {
    if (productId) {
      getProportions()
    }
  }, [productId, getProportions])

  // Прокрутка до опций при изменении фильтра или загрузке данных
  useEffect(() => {
    const proportionIdFilter = searchParams.get('proportion')
    if (proportionIdFilter && proportionsRef.current && productProportions?.values) {
      setTimeout(() => {
        proportionsRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        })
      }, 500)
    }
  }, [searchParams, productProportions])

  // Фильтрация опций по URL параметру
  const proportionIdFilter = searchParams.get('proportion')
  const filteredProportions = proportionIdFilter
    ? productProportions?.values?.filter((item) => item.public_id === proportionIdFilter) || []
    : productProportions?.values || []

  const handleShowAll = () => {
    const newSearchParams = new URLSearchParams(searchParams)
    newSearchParams.delete('proportion')
    setSearchParams(newSearchParams)
  }

  return (
    <>
      <Row className="mb-2" ref={proportionsRef}>
        <Col>Опции товара</Col>
      </Row>

      <Row className="mb-2">
        <Col md="auto">
          <Button onClick={() => setIsOpenProportionModal(true)} variant="success" disabled={!productId}>
            Добавить опцию
          </Button>
        </Col>
        {proportionIdFilter && (
          <Col md="auto">
            <Button onClick={handleShowAll} variant="outline-secondary">
              Показать все опции
            </Button>
          </Col>
        )}
      </Row>

      <Row className="mb-3 g-3">
        {filteredProportions.map((item) => (
          <ProportionCard defaultFormData={item} onUpdateOptions={getProportions} key={item.public_id} />
        ))}

        {isOpenProportionModal && (
          <ProportionModal
            productId={productId}
            isShow={isOpenProportionModal}
            onHide={setIsOpenProportionModal}
            onUpdateOptions={getProportions}
          />
        )}
      </Row>
    </>
  )
}

export default ProductProportions
