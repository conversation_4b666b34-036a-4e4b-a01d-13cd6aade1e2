import useAxios from 'axios-hooks'
import { useEffect } from 'react'
import Select from 'react-select'

import { eventFilterSelectStyles } from './shopSelectStyles'
import { APIRoute } from '../../../../const'
import { getOptionsSelect } from '../../../../utils/common'

function EventsFilter({ events, onFilteredEvents }) {
  const [{ data }, api] = useAxios(
    {
      url: APIRoute.ACTIONS_EVENT_TYPE,
      method: 'GET',
    },
    { manual: false }
  )

  useEffect(() => {
    api()
  }, [api])

  const handleChangeSelect = (items) => {
    if (items.length > 0) {
      const filteredItems = items.map((item) => item.value)
      const filteredEvents = events.filter((event) => filteredItems.includes(event.event_type.public_id))
      onFilteredEvents(filteredEvents)
    } else {
      onFilteredEvents(events)
    }
  }

  return (
    <div className="mb-2">
      Фильтр по типу:
      <Select
        isMulti
        name="colors"
        onChange={handleChangeSelect}
        options={getOptionsSelect(data?.values, 'public_id', 'title')}
        styles={eventFilterSelectStyles}
        className="basic-multi-select"
        classNamePrefix="select"
        placeholder="Выберите тип события"
      />
    </div>
  )
}

export default EventsFilter
