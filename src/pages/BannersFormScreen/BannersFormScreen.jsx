import useAxios from 'axios-hooks'
import { createRef, useEffect, useState, useCallback } from 'react'
import { Button, Col, Figure, FloatingLabel, Form, FormControl, FormSelect, Row } from 'react-bootstrap'
import { Link, useNavigate, useParams } from 'react-router-dom'

import Layout from '@/components/Layout/Layout'

import { APIRoute, MAX_IMG_SIZE } from '@/const'
import { useToast } from '@/hooks/useToast'
import { checkUploadedImage, convertBase64 } from '@/utils/common'
import { unixToMoment } from '@/utils/date'
import { updateFormData } from '@/utils/forms'
import { getImageSrc } from '@/utils/images'

import styles from './BannersFormScreen.module.scss'
import { widthHeightBanners } from './bannersFormScreenData'

function BannersFormScreen() {
  const [defaultFormData, setDefaultFormData] = useState({})
  const [formData, setFormData] = useState({})
  const [newPriorityNumber, setNewPriorityNumber] = useState(0)
  const [bannerType, setBannerType] = useState(formData?.banner_type || defaultFormData?.banner_type)
  const [isReklama, setIsReklama] = useState(false)
  const [isWhiteBackground, setIsWhiteBackground] = useState(true)
  const [bannersLabel, setBannersLabel] = useState({
    desktop: '',
    mobile: '',
  })
  const imgDesktopInputRef = createRef()

  const navigate = useNavigate()
  const { banner_type, public_id } = useParams()
  const openToast = useToast()

  const desktopImgInput = createRef()
  const mobileImgInput = createRef()

  const isUpdateItem = !!banner_type && !!public_id

  const [, api] = useAxios(
    {
      url: APIRoute.ACTIONS_BANNERS,
      method: 'POST',
    },
    { manual: true }
  )
  const [{ data }, apiGetBanners] = useAxios(
    {
      url: APIRoute.ACTIONS_BANNERS,
      method: 'GET',
    },
    { manual: true }
  )
  const [, apiGetPriorityBanners] = useAxios(
    {
      url: APIRoute.ACTIONS_BANNERS,
      method: 'GET',
    },
    { manual: true }
  )

  const updateBannersLabel = useCallback(
    (type, defaultPage) => {
      const page = defaultPage ?? formData.page

      if (page) {
        const bannerWidth = widthHeightBanners[page][type]?.picture_main?.width
        const bannerHeight = widthHeightBanners[page][type]?.picture_main?.height
        const bannerWidthMobile = widthHeightBanners[page][type]?.picture_small?.width
        const bannerHeightMobile = widthHeightBanners[page][type]?.picture_small?.height
        const str =
          bannerWidth && bannerHeight && bannerWidth !== 'all' && bannerHeight !== 'all'
            ? `(${bannerWidth}х${bannerHeight})`
            : ''
        const strMobile =
          bannerWidthMobile && bannerHeightMobile && bannerWidthMobile !== 'all' && bannerHeightMobile !== 'all'
            ? `(${bannerWidthMobile}х${bannerHeightMobile})`
            : ''

        setBannersLabel({ desktop: str, mobile: strMobile })
      }
    },
    [formData.page]
  )

  useEffect(() => {
    if (Object.keys(defaultFormData).length === 0 && banner_type && public_id && data) {
      const findItem = data.values.find((item) => item.banner_type === banner_type && item.public_id === public_id)

      if (findItem) {
        setDefaultFormData({ ...findItem })
        updateBannersLabel(findItem.banner_type, findItem.page)

        if (findItem?.reklama && Object.keys(findItem.reklama).length > 0) {
          setIsReklama(true)
        }
        if (findItem?.reklama_mobile && Object.keys(findItem.reklama_mobile).length > 0) {
          setIsReklama(true)
        }
      }
    } else if (banner_type && !data) {
      apiGetBanners({ url: `${APIRoute.ACTIONS_BANNERS}/${banner_type}` })
    } else {
      setFormData((prev) => ({ ...prev, public: true }))
    }
  }, [apiGetBanners, banner_type, public_id, data, defaultFormData, updateBannersLabel])

  useEffect(() => {
    if (bannerType && newPriorityNumber > 0) {
      setFormData((prev) => ({ ...prev, banner_type: bannerType, priority_number: newPriorityNumber }))
      /*   setBannerType(''); */
      setNewPriorityNumber(0)
    }
  }, [bannerType, newPriorityNumber])

  useEffect(() => {
    if (formData?.banner_type || defaultFormData?.banner_type || !bannerType) {
      setBannerType(formData?.banner_type || defaultFormData?.banner_type)
    }
  }, [bannerType, formData?.banner_type, defaultFormData?.banner_type])

  const handleSubmitForm = (evt) => {
    evt.preventDefault()

    if (isUpdateItem) {
      api({
        url: `${APIRoute.ACTIONS_BANNERS}/${defaultFormData.public_id}`,
        method: 'PUT',
        data: formData,
      }).then((r) => {
        if (r.status === 200) {
          openToast.success({ message: 'Баннер изменён' })
          navigate('/banners')
        }
      })
    } else {
      api({ data: formData }).then((r) => {
        if (r.status === 200) {
          openToast.success({ message: 'Баннер добавлен' })
          navigate('/banners')
        }
      })
    }
  }

  const handleChangeField = (evt) => {
    updateFormData(evt, formData, setFormData, defaultFormData)
  }

  const handleChangeReklamaField = (evt) => {
    const name = evt.target.name
    const value = evt.target.value
    const newFormData = { ...formData }

    if (value === '') {
      delete newFormData.reklama?.[name]
      setFormData({ ...newFormData })
    } else {
      setFormData({
        ...formData,
        reklama: { ...defaultFormData?.reklama, ...formData?.reklama, [name]: value },
      })
    }
  }
  const handleChangeReklamaMobileField = (evt) => {
    const name = evt.target.name
    const value = evt.target.value
    const newFormData = { ...formData }

    if (value === '') {
      delete newFormData.reklama_mobile?.[name]
      setFormData({ ...newFormData })
    } else {
      setFormData({
        ...formData,
        reklama_mobile: { ...defaultFormData?.reklama_mobile, ...formData?.reklama_mobile, [name]: value },
      })
    }
  }

  const handleToggleReklama = (evt) => {
    setIsReklama(evt.target.checked)

    if (!evt.target.checked && 'reklama' in defaultFormData && Object.keys(defaultFormData.reklama).length > 0) {
      setFormData((prev) => ({ ...prev, reklama: {} }))
    } else if (!evt.target.checked && 'reklama' in formData) {
      setFormData((current) => {
        const { reklama: _reklama, ...rest } = current

        return rest
      })
    } else if (evt.target.checked && 'reklama' in formData) {
      setFormData((current) => {
        const { reklama: _reklama, ...rest } = current

        return rest
      })
    }
    if (
      !evt.target.checked &&
      'reklama_mobile' in defaultFormData &&
      Object.keys(defaultFormData.reklama_mobile).length > 0
    ) {
      setFormData((prev) => ({ ...prev, reklama_mobile: {} }))
    } else if (!evt.target.checked && 'reklama_mobile' in formData) {
      setFormData((current) => {
        const { reklama_mobile: _reklama_mobile, ...rest } = current

        return rest
      })
    } else if (evt.target.checked && 'reklama_mobile' in formData) {
      setFormData((current) => {
        const { reklama_mobile: _reklama_mobile, ...rest } = current

        return rest
      })
    }
  }

  const handleFileRead = async (evt) => {
    const name = evt.target.name
    const file = evt.target.files[0]
    const fileSizeInKB = file.size
    const base64 = await convertBase64(file)
    const type = formData.banner_type || defaultFormData.banner_type
    const page = formData.page || defaultFormData.page

    const newFormData = { ...formData }

    if (base64 === '') {
      delete newFormData[name]
      setFormData({ ...newFormData })
    } else if (fileSizeInKB <= MAX_IMG_SIZE) {
      if (
        widthHeightBanners?.[page]?.[type] &&
        widthHeightBanners?.[page]?.[type]?.[name] &&
        (await checkUploadedImage(
          file,
          widthHeightBanners[page][type][name].width,
          widthHeightBanners[page][type][name].height
        ))
      ) {
        setFormData({ ...formData, [name]: base64 })
      } else if (
        widthHeightBanners?.[page] &&
        widthHeightBanners?.[page]?.[type]?.[name] &&
        !(await checkUploadedImage(
          file,
          widthHeightBanners[page][type][name].width,
          widthHeightBanners[page][type][name].height
        ))
      ) {
        openToast.error({
          title: true,
          message: `Выберите изображение с разрешением ${widthHeightBanners?.[page]?.[type]?.[name].width}х${widthHeightBanners?.[page]?.[type]?.[name]?.height}`,
          duration: 6000,
        })
      }
    } else if (fileSizeInKB > MAX_IMG_SIZE) {
      openToast.error({
        title: true,
        message: `Файл слишком большой: ${file.name}`,
        duration: 6000,
      })
    }
  }

  const handleChangeToggle = (evt) => {
    const name = evt.target.name
    const value = evt.target.checked

    setFormData({ ...formData, [name]: value })
  }

  const handleChangeType = (evt) => {
    const bannerType = evt.target.value
    const newFormData = { ...formData }

    desktopImgInput.current.value = ''
    mobileImgInput.current.value = ''

    delete newFormData.picture_main
    delete newFormData.picture_small

    setBannerType(bannerType)
    setFormData({ ...newFormData })

    if (evt.target.value === banner_type) {
      setNewPriorityNumber(+public_id)
    } else {
      apiGetPriorityBanners({ url: `${APIRoute.ACTIONS_BANNERS}/${evt.target.value}` }).then((r) => {
        if (r.status === 200 && r.data?.values?.length > 0) {
          setNewPriorityNumber(r.data.values[r.data.values.length - 1].priority_number + 1)
        } else if (r.status === 200 && r.data?.values?.length === 0) {
          setNewPriorityNumber(1)
        }
      })
    }
    if (bannerType === 'shop' || bannerType === 'external_shop') {
      updateBannersLabel(bannerType, 'shop')
    } else {
      updateBannersLabel(bannerType)
    }
  }

  const handleDeletePictures = (field) => {
    const newFormData = { ...formData }

    delete newFormData[field]

    setFormData({ ...newFormData })

    if (field === 'desktop') imgDesktopInputRef.current.value = ''
  }

  const filterTypeOptions = (page) => {
    switch (page) {
      case 'main':
        return [
          { value: 'big', label: 'Большой слайдер' },
          { value: 'small', label: 'Маленький слайдер' },
          { value: 'story', label: 'Сторис' },
          { value: 'ad1', label: 'Первый блок' },
          { value: 'ad2', label: 'Второй блок' },
          { value: 'projects', label: 'Партнеры проектов' },
        ]
      case 'shop':
        return [
          { value: 'shop', label: 'Слайдер' },
          { value: 'external_shop', label: 'Внешний магазин' },
        ]
      case 'partners':
        return [
          { value: 'partners_main', label: 'Главные партнёры' },
          { value: 'partners', label: 'Партнёры' },
          { value: 'sponsors', label: 'Спонсоры' },
        ]
      case 'events':
        return [{ value: 'events', label: 'События' }]
      default:
        return []
    }
  }

  const returnDateValue = (formData, defaultFormData) => {
    if (Object.prototype.hasOwnProperty.call(formData, 'validity')) {
      return formData.validity || ''
    } else if (defaultFormData?.validity) {
      return defaultFormData.validity > 0 ? unixToMoment(defaultFormData.validity).format('YYYY-MM-DDTHH:mm:ss') : ''
    }

    return ''
  }

  return (
    <Layout>
      <Row className="mb-3">
        <Col>
          <Button as={Link} to={'/banners'} variant="outline-secondary">
            <i className="bi bi-arrow-left me-2" />
            Вернуться к списку баннеров
          </Button>
        </Col>
      </Row>
      <Row>
        <h3 className="mb-3">{isUpdateItem ? 'Редактирование баннера' : 'Добавление баннера'}</h3>
      </Row>
      <Form onSubmit={handleSubmitForm}>
        <Row className="g-3 mb-3">
          <Col md={4}>
            <FloatingLabel controlId="titleLabel" label="Заголовок">
              <FormControl
                onChange={handleChangeField}
                defaultValue={defaultFormData.title}
                name="title"
                type="text"
                placeholder="Заголовок"
              />
            </FloatingLabel>
          </Col>

          <Col md={4}>
            <FloatingLabel controlId="pageLabel" label="Страница">
              <FormSelect
                onChange={handleChangeField}
                value={formData?.page || defaultFormData?.page}
                name="page"
                aria-label="Страница"
              >
                <option>выберите один из вариантов</option>
                <option value="main">Главная</option>
                <option value="shop">Интернет-магазин</option>
                <option value="partners">Партнёры</option>
                <option value="events">События</option>
              </FormSelect>
            </FloatingLabel>
          </Col>

          <Col md={4}>
            <FloatingLabel controlId="bannerTypeLabel" label="Тип баннера">
              <FormSelect
                onChange={handleChangeType}
                value={formData?.banner_type || defaultFormData?.banner_type}
                name="banner_type"
                aria-label="Тип баннера"
                disabled={!formData?.page && !defaultFormData?.page}
              >
                <option>выберите один из вариантов</option>
                {filterTypeOptions(formData?.page || defaultFormData?.page).map((item) => (
                  <option value={item.value} key={item.value}>
                    {item.label}
                  </option>
                ))}
              </FormSelect>
            </FloatingLabel>
          </Col>

          <Col md={4}>
            <FloatingLabel controlId="linkLabel" label="Ссылка банера">
              <FormControl
                onChange={handleChangeField}
                defaultValue={defaultFormData.link}
                name="link"
                type="text"
                placeholder="Урл банера"
              />
            </FloatingLabel>
          </Col>

          <Col md={4}>
            <FloatingLabel controlId="priority_numberProductLabel" label="Приоритет">
              <FormControl
                onChange={handleChangeField}
                value={formData?.priority_number || defaultFormData?.priority_number || ''}
                name="priority_number"
                type="number"
                placeholder="Приоритет"
              />
            </FloatingLabel>
          </Col>

          {bannerType === 'external_shop' && (
            <>
              <Col md={4}>
                <FloatingLabel controlId="price" label="Цена">
                  <FormControl
                    onChange={handleChangeField}
                    value={formData?.price || defaultFormData?.price || ''}
                    name="price"
                    type="number"
                    placeholder="Цена"
                  />
                </FloatingLabel>
              </Col>

              <Col md={4}>
                <FloatingLabel controlId="old_price" label="Старая цена">
                  <FormControl
                    onChange={handleChangeField}
                    value={formData?.old_price || defaultFormData?.old_price || ''}
                    name="old_price"
                    type="number"
                    placeholder="Старая цена"
                  />
                </FloatingLabel>
              </Col>
            </>
          )}

          <Col className="d-flex align-items-center" md="auto">
            <Form.Check
              onChange={handleChangeToggle}
              checked={Boolean(formData?.public ?? defaultFormData?.public)}
              name="public"
              type="switch"
              id="public-switch"
              label="Отображать на странице"
            />
          </Col>

          <Col md={4}>
            <FloatingLabel controlId="subtitleLabel" label="Подзаголовок">
              <FormControl
                onChange={handleChangeField}
                defaultValue={defaultFormData.subtitle}
                name="subtitle"
                type="text"
                placeholder="Подзаголовок"
              />
            </FloatingLabel>
          </Col>

          <Col md={4}>
            <FloatingLabel controlId="descriptionLabel" label="Описание">
              <FormControl
                onChange={handleChangeField}
                defaultValue={defaultFormData.description}
                name="description"
                type="text"
                placeholder="Описание"
              />
            </FloatingLabel>
          </Col>

          <Col md={4}>
            <FloatingLabel controlId="footnoteLabel" label="Примечание">
              <FormControl
                onChange={handleChangeField}
                defaultValue={defaultFormData.footnote}
                name="footnote"
                type="text"
                placeholder="Примечание"
              />
            </FloatingLabel>
          </Col>

          <Col md={4}>
            <FloatingLabel controlId="validityInput" label="Срок действия">
              <Form.Control
                onChange={handleChangeField}
                name="validity"
                type="datetime-local"
                value={returnDateValue(formData, defaultFormData)}
                placeholder="Срок действия"
                required
              />
            </FloatingLabel>
          </Col>

          <Col className="d-flex align-items-center" md={12}>
            <Form.Check
              onChange={handleToggleReklama}
              checked={Boolean(isReklama)}
              name="reklama"
              type="switch"
              id="reklama-switch"
              label="Реклама"
            />
          </Col>

          {isReklama && (
            <>
              <div style={{ display: 'flex', flexDirection: 'column' }}>
                <h4>Дэсктоп</h4>
                <Row className="g-3 mb-3">
                  <Col md={4}>
                    <FloatingLabel controlId="reklamaNameLabel" label="Название">
                      <FormControl
                        onChange={handleChangeReklamaField}
                        defaultValue={defaultFormData?.reklama?.name}
                        name="name"
                        type="text"
                        placeholder="Название"
                      />
                    </FloatingLabel>
                  </Col>

                  <Col md={4}>
                    <FloatingLabel controlId="reklamaInnLabel" label="ИНН">
                      <FormControl
                        onChange={handleChangeReklamaField}
                        defaultValue={defaultFormData?.reklama?.inn}
                        name="inn"
                        type="text"
                        placeholder="ИНН"
                      />
                    </FloatingLabel>
                  </Col>

                  <Col md={4}>
                    <FloatingLabel controlId="reklamaGovIdLabel" label="Идентификатор">
                      <FormControl
                        onChange={handleChangeReklamaField}
                        defaultValue={defaultFormData?.reklama?.gov_id}
                        name="gov_id"
                        type="text"
                        placeholder="Идентификатор"
                      />
                    </FloatingLabel>
                  </Col>

                  <Col md={4}>
                    <FloatingLabel controlId="reklamaRestrictionLabel" label="Ограничение">
                      <FormControl
                        onChange={handleChangeReklamaField}
                        defaultValue={defaultFormData?.reklama?.restriction}
                        name="restriction"
                        type="text"
                        placeholder="Ограничение"
                      />
                    </FloatingLabel>
                  </Col>
                </Row>
              </div>
            </>
          )}

          {isReklama && (
            <>
              <div style={{ display: 'flex', flexDirection: 'column' }}>
                <h4>Мобилка</h4>
                <Row className="g-3 mb-3">
                  <Col md={4}>
                    <FloatingLabel controlId="reklamaNameLabel" label="Название">
                      <FormControl
                        onChange={handleChangeReklamaMobileField}
                        defaultValue={defaultFormData?.reklama_mobile?.name}
                        name="name"
                        type="text"
                        placeholder="Название"
                      />
                    </FloatingLabel>
                  </Col>

                  <Col md={4}>
                    <FloatingLabel controlId="reklamaInnLabel" label="ИНН">
                      <FormControl
                        onChange={handleChangeReklamaMobileField}
                        defaultValue={defaultFormData?.reklama_mobile?.inn}
                        name="inn"
                        type="text"
                        placeholder="ИНН"
                      />
                    </FloatingLabel>
                  </Col>

                  <Col md={4}>
                    <FloatingLabel controlId="reklamaGovIdLabel" label="Идентификатор">
                      <FormControl
                        onChange={handleChangeReklamaMobileField}
                        defaultValue={defaultFormData?.reklama_mobile?.gov_id}
                        name="gov_id"
                        type="text"
                        placeholder="Идентификатор"
                      />
                    </FloatingLabel>
                  </Col>

                  <Col md={4}>
                    <FloatingLabel controlId="reklamaRestrictionLabel" label="Ограничение">
                      <FormControl
                        onChange={handleChangeReklamaField}
                        defaultValue={defaultFormData?.reklama?.restriction}
                        name="restriction"
                        type="text"
                        placeholder="Ограничение"
                      />
                    </FloatingLabel>
                  </Col>
                </Row>
              </div>
            </>
          )}

          <Col className="mb-3" md={6}>
            <Form.Group controlId="desktopFile">
              <Form.Control
                className={`${styles.fileInput} visually-hidden`}
                type="file"
                onChange={handleFileRead}
                name="picture_main"
                ref={desktopImgInput}
                disabled={!(formData.banner_type || defaultFormData.banner_type)}
              />
              <Form.Label className={styles.fileButton}>Основная картинка {bannersLabel.desktop}</Form.Label>
            </Form.Group>
            {!(formData.banner_type || defaultFormData.banner_type) && <p>выберите тип баннера</p>}
          </Col>
          <Col className="mb-3" md={6}>
            <Form.Group controlId="mobileFile">
              <Form.Control
                className={`${styles.fileInput} visually-hidden`}
                type="file"
                onChange={handleFileRead}
                name="picture_small"
                ref={mobileImgInput}
                disabled={
                  !(formData.banner_type || defaultFormData.banner_type) ||
                  formData.banner_type === 'story' ||
                  formData.banner_type === 'shop'
                }
              />
              <Form.Label className={styles.fileButton}>Мобильная картинка, если есть {bannersLabel.mobile}</Form.Label>
            </Form.Group>
            {!(formData.banner_type || defaultFormData.banner_type) && <p>выберите тип баннера</p>}
          </Col>
        </Row>
        <Row className="mb-5">
          <Col className="d-grid justify-content-center" md={6}>
            {(formData?.picture_main || defaultFormData?.picture_main) && (
              <>
                <Figure className={`${styles.imgWrap} ${isWhiteBackground ? styles.imgWrapWhite : ''} mb-0`}>
                  <Figure.Image
                    className="mb-0"
                    width={260}
                    alt="Десктопная картинка"
                    src={getImageSrc(formData?.picture_main || defaultFormData?.picture_main)}
                    ref={imgDesktopInputRef}
                  />
                </Figure>
                {!isUpdateItem && (
                  <Row>
                    <Col>
                      <Button onClick={() => handleDeletePictures('picture_main')} variant="link" size="sm">
                        удалить
                      </Button>
                    </Col>
                  </Row>
                )}
              </>
            )}
          </Col>
          <Col className="d-grid justify-content-center" md={6}>
            {(formData?.picture_small || defaultFormData?.picture_small) && (
              <>
                <Figure className={`${styles.imgWrap} ${isWhiteBackground ? styles.imgWrapWhite : ''} mb-0`}>
                  <Figure.Image
                    className="mb-0"
                    width={260}
                    alt="Мобильная картинка"
                    src={getImageSrc(formData?.picture_small || defaultFormData?.picture_small)}
                  />
                </Figure>
                {!isUpdateItem && (
                  <Row>
                    <Col>
                      <Button onClick={() => handleDeletePictures('picture_small')} variant="link" size="sm">
                        удалить
                      </Button>
                    </Col>
                  </Row>
                )}
              </>
            )}
          </Col>
        </Row>

        <Row className="mb-3">
          <Col />
          <Col md="auto">
            <Button onClick={() => setIsWhiteBackground((prev) => !prev)} size="sm">
              Сменить фон баннера
            </Button>
          </Col>
        </Row>

        <Row className="mb-5">
          <Col className="d-grid" md={{ offset: 4, span: 4 }}>
            <Button type="submit" variant="success" size="lg">
              Сохранить
            </Button>
          </Col>
        </Row>
      </Form>
    </Layout>
  )
}

export default BannersFormScreen
