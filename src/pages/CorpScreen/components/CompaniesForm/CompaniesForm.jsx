import { nanoid } from '@reduxjs/toolkit'
import React from 'react'
import { Col, FloatingLabel, Form, FormControl, Row } from 'react-bootstrap'
import { useForm } from 'react-hook-form'

import ImageField from '@/components/Forms/ImageField/ImageField'

import { useCreateCompany, useUpdateCompany } from '@/features/corp/api'
import { removeEmptyString } from '@/utils/common'
import { checkSetValue } from '@/utils/forms'

import styles from './CompaniesForm.module.scss'

const fields = [
  {
    id: nanoid(),
    name: 'name',
    label: 'Название',
    type: 'text',
    required: true,
  },
  {
    id: nanoid(),
    name: 'inn',
    label: 'ИНН',
    type: 'text',
    pattern: /[0-9]{10}$/,
    required: false,
  },
  {
    id: nanoid(),
    name: 'fullname',
    label: 'Полное имя',
    type: 'text',
    required: false,
  },
  {
    id: nanoid(),
    name: 'description',
    label: 'Описание',
    type: 'text',
    required: false,
  },
]

const contactFields = [
  {
    id: nanoid(),
    name: 'contact.name',
    label: 'Имя',
    type: 'text',
    required: false,
  },
  {
    id: nanoid(),
    name: 'contact.title',
    label: 'Должность',
    type: 'text',
    required: false,
  },
  {
    id: nanoid(),
    name: 'contact.phone',
    label: 'Телефон (формат: 79998887766)',
    type: 'tel',
    pattern: /[7][0-9]{10}$/,
    required: false,
  },
  {
    id: nanoid(),
    name: 'contact.email',
    label: 'Электронная почта',
    type: 'email',
    required: false,
  },
]

function CompaniesForm({ selectedItem, onCloseModal }) {
  const {
    register,
    setValue,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      name: selectedItem?.name,
      inn: selectedItem?.inn,
      fullname: selectedItem?.fullname,
      description: selectedItem?.description,
      contact: {
        name: selectedItem?.contact?.name,
        title: selectedItem?.contact?.title,
        phone: selectedItem?.contact?.phone,
        email: selectedItem?.contact?.email,
      },
    },
  })

  const createCompanyMutation = useCreateCompany()
  const updateCompanyMutation = useUpdateCompany()

  const isEdit = Object.keys(selectedItem).length > 0

  const onSubmit = (data) => {
    const filteredData = removeEmptyString(data)
    const filteredDataSecond = removeEmptyString(filteredData)

    if (Object.keys(filteredDataSecond).length > 0) {
      if (isEdit) {
        updateCompanyMutation.mutate(
          {
            companyId: selectedItem.public_id,
            data: filteredDataSecond,
          },
          {
            onSuccess: () => {
              onCloseModal()
            },
          }
        )
      } else {
        createCompanyMutation.mutate(filteredDataSecond, {
          onSuccess: () => {
            onCloseModal()
          },
        })
      }
    }
  }

  return (
    <Form onSubmit={handleSubmit(onSubmit)} id="form">
      <Row className="g-3 mb-3">
        <ImageField
          fieldName="logo"
          isClickRow={false}
          imagePath={selectedItem?.logo}
          isEdit={isEdit}
          setValue={setValue}
        />

        <h5 className="mb-0 mt-4">Компания</h5>
        {fields.map((field) => (
          <Col md={12} key={field.id}>
            <Form.Group>
              <FloatingLabel controlId={`${field.name}Input`} label={field.label}>
                <FormControl
                  className={styles.field}
                  {...register(field.name, {
                    required: field.required && !isEdit,
                    pattern: field.pattern,
                    setValueAs: (v) => checkSetValue(v, selectedItem?.[field.name], field.type),
                  })}
                  type={field.type}
                  isInvalid={errors[field.name]}
                  placeholder={field.label}
                />
              </FloatingLabel>
            </Form.Group>
          </Col>
        ))}

        <h5 className="mb-0 mt-4">Контакты</h5>
        {contactFields.map((field) => (
          <Col md={12} key={field.id}>
            <Form.Group>
              <FloatingLabel controlId={`${field.name}Input`} label={field.label}>
                <FormControl
                  className={styles.field}
                  {...register(field.name, {
                    required: field.required && !isEdit,
                    pattern: field.pattern,
                    setValueAs: (v) => checkSetValue(v, selectedItem?.[field.name], field.type),
                  })}
                  type={field.type}
                  isInvalid={errors[field.name]}
                  placeholder={field.label}
                />
              </FloatingLabel>
            </Form.Group>
          </Col>
        ))}
      </Row>
    </Form>
  )
}

export default CompaniesForm
